<?php
if (!defined('BASEPATH'))
	exit('No direct script access allowed');
//print_r($item_lookup);exit;
?>
<?php echo form_open($form_action, [
	'id' => 'form_group',
	'name' => 'form_group',
	'rule' => 'form',
	'class' => ''
]); ?>

<div class="row">
	<div class="col-md-offset-2 col-md-8">
		<div class="panel panel-default">
			<div class="panel-heading">
				<div class="panel-bars">
					<ul class="btn-bars">
						<li class="dropdown">
							<a data-toggle="dropdown" class="dropdown-toggle" href="javascript:;">
								<i class="fa fa-bars fa-lg tip" data-placement="left" title="<?php echo lang("actions") ?>"></i>
							</a>
							<ul class="dropdown-menu pull-right" role="menu">
								<li>
									<a href="<?php echo site_url("{$nameroutes}/create"); ?>">
										<i class="fa fa-plus"></i> <?php echo lang('action:add') ?>
									</a>
								</li>
							</ul>
						</li>
					</ul>
				</div>
				<h3 class="panel-title"><?php echo (@$is_edit) ? lang('heading:service_group_update_lab') : lang('heading:service_group_create_lab'); ?></h3>
			</div>
			<div class="panel-body table-responsive">
				<div class="row">
					<div class="col-md-12 col-xs-12">
						<!-- <div class="form-group">
							<?php echo form_label("Group Pemeriksaan" . ' *', 'Group_Pemeriksaan', ['class' => 'control-label col-md-3']) ?>
							<div class="col-md-9">
								<?php echo form_input('f[Group_Pemeriksaan]', set_value('f[Group_Pemeriksaan]', $item->Group_Pemeriksaan, TRUE), [
									'id' => 'Group_Pemeriksaan',
									'required' => 'required',
									'class' => 'form-control'
								]); ?>
							</div>
						</div> -->
						<div class="form-group">
							<?php echo form_label("Group Pemeriksaan" . ' *', 'Group_Pemeriksaan', ['class' => 'control-label col-md-3']) ?>
							<div class="col-md-9">
								<?php echo form_dropdown('f[Group_Pemeriksaan]', $dropdown_group_pemeriksaan, set_value('f[Group_Pemeriksaan]', @$item->Group_Pemeriksaan, TRUE), [
									'id' => 'Group_Pemeriksaan',
									'class' => 'form-control'
								]); ?>
							</div>
						</div>
						<div class="form-group">
							<?php echo form_label("Nama Pemeriksaan", 'Nama_Pemeriksaan', ['class' => 'control-label col-md-3']) ?>
							<div class="col-md-9">
								<?php echo form_input('f[Nama_Pemeriksaan]', set_value('f[Nama_Pemeriksaan]', @$item->Nama_Pemeriksaan, TRUE), [
									'id' => 'Nama_Pemeriksaan',
									'class' => 'form-control'
								]); ?>
							</div>
						</div>
						<div class="form-group">
							<?php echo form_label("Satuan", 'Satuan', ['class' => 'control-label col-md-3']) ?>
							<div class="col-md-9">
								<?php echo form_input('f[Satuan]', set_value('f[Satuan]', @$item->Satuan, TRUE), [
									'id' => 'Satuan',
									'class' => 'form-control'
								]); ?>
							</div>
						</div>
						<div class="form-group">
							<?php echo form_label("Nilai Rujukan", 'Nilai_Rujukan', ['class' => 'control-label col-md-3']) ?>
							<div class="col-md-9">
								<?php echo form_input('f[Nilai_Rujukan]', set_value('f[Nilai_Rujukan]', @$item->Nilai_Rujukan, TRUE), [
									'id' => 'Nilai_Rujukan',
									'class' => 'form-control'
								]); ?>
							</div>
						</div>
						<hr />
					</div>
				</div>
				<hr />
				<div class="row">
					<div class="col-md-12">
						<div class="form-group text-right">
							<button id="js-btn-submit" type="submit" class="btn btn-primary"><?php echo lang('buttons:save') ?></button>
							<button class="btn btn-warning" type="button" onclick="window.location='<?php echo base_url("{$nameroutes}/create") ?>';">New</button>
							<button class="btn btn-default" type="button" onclick="window.location='<?php echo base_url("{$nameroutes}") ?>';">Close</button>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<?php echo form_close() ?>
<script type="text/javascript">
	//<![CDATA[
	(function($) {
		var _form = $("#form_group");
		var _form_actions = {
			init: function() {

				if (_form.find('select#PostinganKe').val() == 'Hutang') {
					_form.find('select#HutangKe').val('None');
					_form.find('select#HutangKe').prop('disabled', 'disabled');
				}

				_form.find('select#PostinganKe').on("change", function() {
					if ($(this).val() == 'Hutang') {
						_form.find('select#HutangKe').val('None');
						_form.find('select#HutangKe').prop('disabled', 'disabled');
					} else {
						_form.find('select#HutangKe').removeProp('disabled');
					}
				});

				_form.find('a.btn-clear').on('click', function(e) {
					var _target_class = $(this).data('target-class');
					$('.' + _target_class).val('');
				});


			}
		}

		$(document).ready(function(e) {

			_form_actions.init();

			_form.on("submit", function(e) {
				e.preventDefault();

				$.post(_form.prop("action"), _form.serializeArray(), function(response, status, xhr) {

					if ("error" == response.status) {
						$.alert_error(response.message);
						return false
					}

					$.alert_success(response.message);

					setTimeout(function() {

						document.location.href = "<?php echo base_url($nameroutes); ?>";

					}, 300);

				});
			});



		});

	})(jQuery);
	//]]>
</script>