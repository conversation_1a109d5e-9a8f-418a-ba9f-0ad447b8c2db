<?php
if (!defined('BASEPATH'))
	exit('No direct script access allowed');
//print_r($item_lookup);exit;
?>
<?php echo form_open($form_action, [
	'id' => 'form_room',
	'name' => 'form_room',
	'rule' => 'form',
	'class' => ''
]); ?>
<style>
	td {
		cursor: pointer !important;
	}
</style>
<div class="row">
	<div class="col-md-offset-2 col-md-8">
		<div class="panel panel-default">
			<div class="panel-heading">
				<div class="panel-bars">
					<ul class="btn-bars">
						<li class="dropdown">
							<a data-toggle="dropdown" class="dropdown-toggle" href="javascript:;">
								<i class="fa fa-bars fa-lg tip" data-placement="left" title="<?php echo lang("actions") ?>"></i>
							</a>
							<ul class="dropdown-menu pull-right" role="menu">
								<li>
									<a href="<?php echo site_url("{$nameroutes}/create"); ?>">
										<i class="fa fa-plus"></i> <?php echo lang('action:add') ?>
									</a>
								</li>
							</ul>
						</li>
					</ul>
				</div>
				<h3 class="panel-title"><?php echo (@$is_edit) ? lang('heading:room_update') : lang('heading:room_create'); ?></h3>
			</div>
			<div class="panel-body table-responsive">
				<div class="row">
					<div class="col-md-12 col-xs-12">
						<div class="form-group">
							<?php echo form_label(lang('label:room_number') . ' *', 'NoKamar', ['class' => 'control-label col-md-3']) ?>
							<div class="col-md-9">
								<?php echo form_input('f[NoKamar]', set_value('f[NoKamar]', @$item->NoKamar, FALSE), [
									'id' => 'NoKamar',
									'placeholder' => '',
									'required' => 'required',
									'class' => 'form-control',
									'autocomplete' => 'off'
								]); ?>
							</div>
						</div>
						<div class="form-group">
							<?php echo form_label(lang('label:sal') . ' *', 'SalID', ['class' => 'control-label col-md-3']) ?>
							<div class="col-md-9">
								<?php echo form_dropdown('f[SalID]', $dropdown_sal, set_value('f[SalID]', @$item->SalID, TRUE), [
									'id' => 'SalID',
									'class' => 'form-control'
								]); ?>
							</div>
						</div>
						<div class="form-group">
							<?php echo form_label(lang('label:floor_number') . ' *', 'NoLantai', ['class' => 'control-label col-md-3']) ?>
							<div class="col-md-9">
								<?php echo form_input([
									'name' => 'f[NoLantai]',
									'value' => set_value('f[NoLantai]', @$item->NoLantai, TRUE),
									'id' => 'NoLantai',
									'placeholder' => '',
									'required' => 'required',
									'class' => 'form-control',
									'type' => 'number',
									'autocomplete' => 'off'
								]); ?>
							</div>
						</div>
						<div class="form-group">
							<?php echo form_label(lang('label:class') . ' *', 'KelasID', ['class' => 'control-label col-md-3']) ?>
							<div class="col-md-9">
								<?php echo form_dropdown('f[KelasID]', $dropdown_class, set_value('f[KelasID]', @$item->KelasID, TRUE), [
									'id' => 'KelasID',
									'class' => 'form-control'
								]); ?>
							</div>
						</div>
						<div class="form-group">
							<?php echo form_label('Opsi', 'Tambahan', ['class' => 'control-label col-md-3']) ?>
							<div class="col-md-3">
								<?php echo form_hidden('f[Tambahan]', 0); ?>
								<?php echo form_checkbox([
									'id' => 'Tambahan',
									'name' => 'f[Tambahan]',
									'value' => 1,
									'checked' => set_value('f[Tambahan]', (bool) @$item->Tambahan, TRUE),
									'class' => 'checkbox'
								]) . ' ' . form_label('<b>' . lang('label:added') . '</b>', 'Tambahan'); ?>
							</div>
							<div class="col-md-3">
								<?php echo form_hidden('f[DipakaiBOR]', 0); ?>
								<?php echo form_checkbox([
									'id' => 'DipakaiBOR',
									'name' => 'f[DipakaiBOR]',
									'value' => 1,
									'checked' => set_value('f[DipakaiBOR]', (bool) @$item->DipakaiBOR, TRUE),
									'class' => 'checkbox'
								]) . ' ' . form_label('<b>' . lang('label:bor') . '</b>', 'DipakaiBOR'); ?>
							</div>
							<div class="col-md-3">
								<?php echo form_hidden('f[Active]', 0); ?>
								<?php echo form_checkbox([
									'id' => 'Active',
									'name' => 'f[Active]',
									'value' => 1,
									'checked' => set_value('f[Active]', (bool) @$item->Active, TRUE),
									'class' => 'checkbox'
								]) . ' ' . form_label('<b>' . 'Active' . '</b>', 'Active'); ?>
							</div>
						</div>
					</div>
				</div>
				<br>
				<div class="row">
					<div class="col-md-12">
						<div class="form-room">
							<table id="dt_room_detail" class="table table-bordered" width="100%" cellspacing="0">
								<thead>
									<tr>
										<th style="width:65px;text-align:center;"><i class="fa fa-cog"></i></th>
										<th><?php echo 'No Bed' ?></th>
										<th style="text-align:center;"><?php echo 'Status' ?></th>
										<th style="text-align:center;"><?php echo 'Dipakai BOR' ?></th>
									</tr>
								</thead>
								<tbody>
								</tbody>
							</table>
						</div>
						<button type="button" id="add_bed" class="btn btn-success btn-block">Tambah BED</button>
					</div>
				</div>
				<hr />
				<div class="row">
					<div class="col-md-12">
						<div class="form-group text-right">
							<button id="js-btn-submit" type="submit" class="btn btn-primary"><?php echo lang('buttons:save') ?></button>
							<button class="btn btn-warning" type="button" onclick="window.location='<?php echo base_url("{$nameroutes}/create") ?>';">New</button>
							<button class="btn btn-default" type="button" onclick="window.location='<?php echo base_url("{$nameroutes}") ?>';">Close</button>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<?php echo form_close() ?>
<script type="text/javascript">
	//<![CDATA[
	(function($) {
		var _form = $("#form_room");

		var _datatable;
		var _datatable_actions = {
			edit: function(row, data, index) {
				switch (this.index()) {
					case 1:
						var _input = $("<input type=\"number\" style=\"width:100%\" value=\"" + data.NoBed + "\" class=\"form-control\">");
						this.empty().append(_input);

						_input.trigger("focus");
						_input.on("blur", function(e) {
							e.preventDefault();
							try {
								data.NoBed = this.value;
								_datatable.row(row).data(data).draw(true);
							} catch (ex) {
								console.log(data)
							}
						});
						break;
					case 3:
						try {
							if (data.DipakaiBORi == 1) {
								data.DipakaiBORi = 0;
							} else {
								data.DipakaiBORi = 1;
							}
							_datatable.row(row).data(data).draw(true);
						} catch (ex) {
							console.log(data)
						};
						break;

				}
			},
			remove: function(params, scope, fn) {
				_datatable.row(scope).remove().draw();
			},
			add_row: function(params, fn, scope) {
				_datatable.row.add({
					"NoBed": '',
					"Status": 1,
					"DipakaiBORi": '',
				}).draw(false);
			}
		};
		$.fn.extend({
			dt_room_detail: function() {
				var _this = this;

				if ($.fn.dataTable.isDataTable(_this.attr("id"))) {
					return _this
				}

				_datatable = _this.DataTable({
					processing: true,
					serverSide: false,
					paginate: false,
					ordering: false,
					searching: false,
					info: false,
					autoWidth: false,
					responsive: true,
					data: <?php print_r(json_encode(@$item_detail, JSON_NUMERIC_CHECK)); ?>,
					columns: [{
							data: "NoBed",
							className: "actions text-center",
							render: function(val, type, row, meta) {
								return String("<a href=\"javascript:;\" title=\"<?php echo lang("buttons:remove") ?>\" class=\"btn btn-danger btn-remove\"><i class=\"fa fa-times\"></i></a>")
							}
						},
						{
							data: "NoBed",
							className: "text-center",
						},
						{
							data: "Status",
							className: "text-center",
						},
						{
							data: "DipakaiBORi",
							className: "text-center",
							render: function(val, type, row) {
								if (val == 1) {
									return '<div class="text-success">Active</div>'
								} else {
									return '<div class="text-danger">Not Active</div>'
								}
							}
						},


					],
					createdRow: function(row, data, index) {
						$(row).on("click", "td", function(e) {
							e.preventDefault();
							var elem = $(e.target);
							_datatable_actions.edit.call(elem, row, data, index);
						});

						$(row).on("click", "a.btn-remove", function(e) {
							e.preventDefault();
							var elem = $(e.target);

							if (confirm("<?php echo lang('global:delete_confirm') ?>, data yang dihapus akan hilang")) {
								_datatable_actions.remove(data, row)
							}
						})
					}
				});

				$("#dt_room_detail_length select, #dt_room_detail_filter input")
					.addClass("form-control");

				return _this
			},

		});

		$(document).ready(function(e) {
			$("#dt_room_detail").dt_room_detail();

			var dataPost = [];
			_form.on("submit", function(e) {
				//$("#js-btn-submit").prop('disabled', true);

				e.preventDefault();
				$.each(_form.serializeArray(), function(i, value) {
					dataPost.push(value); // push data form registrasi ke data post
				});

				var table_data = $("#dt_room_detail").DataTable().rows().data();
				table_data.each(function(v, i) {
					dataPost.push({
						name: 'detail[' + i + '][NoKamar]',
						value: $("#NoKamar").val()
					});
					dataPost.push({
						name: 'detail[' + i + '][NoBed]',
						value: v.NoBed
					});
					dataPost.push({
						name: 'detail[' + i + '][Status]',
						value: v.Status
					});
					dataPost.push({
						name: 'detail[' + i + '][DipakaiBOR]',
						value: v.DipakaiBOR
					});
				});
				$.post(_form.prop("action"), dataPost, function(response, status, xhr) {

					if ("error" == response.status) {
						$("#js-btn-submit").prop('disabled', false);
						$.alert_error(response.message);
						return false
					}

					$.alert_success(response.message);

					var id = response.id;

					setTimeout(function() {

						document.location.href = "<?php echo base_url($nameroutes); ?>";

					}, 300);

				});
			});

			$("#add_bed").click(function() {
				_datatable_actions.add_row();
			});

		});

	})(jQuery);
	//]]>
</script>