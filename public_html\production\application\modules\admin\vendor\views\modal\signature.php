<?php if (!defined('BASEPATH')) exit('No direct script access allowed');

?>
<?php echo form_open($form_action, [
	"id" => "form_crud__delete",
	"name" => "form_crud__delete",
	"role" => "form"
]); ?>
<div class="modal-body">
	<center>
		<div class="row" style="border: 1pt solid black;">
			<canvas width="500%" height="200%" name="p[ttd]" id="ttd" class="canvas"></canvas>
		</div>
	</center>
</div>
<div class="modal-footer">
	<div class="col-md-6">
		<button type="button" id="hapus_ttd" class="btn btn-danger btn-block"><b>Hapus Tanda Tangan</b></button>
	</div>
	<div class="col-md-6">
		<button type="button" id="simpan_ttd" class="btn btn-success btn-block"><b><PERSON>mp<PERSON></b></button>
	</div>
</div>
<?php echo form_close() ?>
<script type="text/javascript">
	//<![CDATA[
	;
	(function($) {
		$(document).ready(function(e) {
			var canvas = document.querySelector("canvas");
			var context = canvas.getContext('2d');
			var signaturePad = new SignaturePad(canvas);
			var Kode_Supplier = $('#Kode_Supplier').val();
			$("#simpan_ttd").on("click", function(e) {
				html2canvas($("#ttd")[0], {
					pixelRatio: 100
				}).then((canvas) => {
					var dataURL = canvas.toDataURL("image/png");
					var data_post = {
						'images': dataURL,
						'Kode_Supplier': Kode_Supplier,
					}

					$.post('<?= base_url("vendor/signature") ?>', data_post, function(response, status, xhr) {
						if ("error" == response.status) {
							$.alert_error(response.status);
							return false
						}

						$.alert_success('Berhasil Menyimpan Tanda Tangan');
						$('.close').click()
						location.reload();
					});
				});

			});

			$("#hapus_ttd").on("click", function(e) {
				context.clearRect(0, 0, canvas.width, canvas.height);
			});

		});
	})(jQuery);
	//]]>
</script>