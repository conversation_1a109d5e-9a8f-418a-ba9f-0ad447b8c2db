<?php
defined('BASEPATH') or exit('No direct script access allowed');

$lang['heading:others'] = 'Others';
$lang['heading:service_type'] = 'Service Type';
$lang['heading:service_type_list'] = 'Service Type List';
$lang['heading:service_type_create'] = 'Create New Service Type';
$lang['heading:service_type_update'] = 'Update Service Type';
$lang['heading:service_type_delete'] = 'Delete Service Type';
$lang['heading:schedule_time'] = 'Schedule Time';
$lang['heading:schedule_time_list'] = 'Schedule Time List';
$lang['heading:schedule_time_create'] = 'Create New Schedule Time';
$lang['heading:schedule_time_update'] = 'Update Schedule Time';
$lang['heading:schedule_time_delete'] = 'Delete Schedule Time';
$lang['heading:discount'] = 'Discount';
$lang['heading:discount_list'] = 'Discount List';
$lang['heading:discount_create'] = 'Create New Discount';
$lang['heading:discount_update'] = 'Update Discount';
$lang['heading:discount_delete'] = 'Delete Discount';
$lang['heading:section'] = 'Section';
$lang['heading:section_list'] = 'Section List';
$lang['heading:section_create'] = 'Create New Section';
$lang['heading:section_update'] = 'Update Section';
$lang['heading:section_delete'] = 'Delete Section';
$lang['heading:section_group'] = 'Section Group';
$lang['heading:section_group_list'] = 'Section Group List';
$lang['heading:section_group_create'] = 'Create New Section Group';
$lang['heading:section_group_update'] = 'Update Section Group';
$lang['heading:section_group_delete'] = 'Delete Section Group';
$lang['heading:payment_type'] = 'Payment Type';
$lang['heading:payment_type_list'] = 'Payment Type List';
$lang['heading:payment_type_create'] = 'Create New Tipe Payment';
$lang['heading:payment_type_update'] = 'Update Type Payment';
$lang['heading:payment_type_delete'] = 'Delete Type Payment';
$lang['heading:room'] = 'Room';
$lang['heading:room_list'] = 'Room List';
$lang['heading:room_create'] = 'Create New Room';
$lang['heading:room_update'] = 'Update Room';
$lang['heading:room_delete'] = 'Delete Room';
$lang['heading:room_status'] = 'Room Status';
$lang['heading:room_status_list'] = 'Room Status List';
$lang['heading:room_status_create'] = 'Create New Room Status';
$lang['heading:room_status_update'] = 'Update Room Status';
$lang['heading:room_status_delete'] = 'Delete Room Status';


$lang['subtitle:others'] = 'Others';

//search
$lang['search:group'] = 'Kelompok';
$lang['search:filter'] = 'Filter';

// label
$lang['label:code'] = 'Code';
$lang['label:name']    = 'Name';
$lang['label:international_name']    = 'International Name';
$lang['label:service_type']    = 'Service Type';
$lang['label:from_hour'] = 'From Hour';
$lang['label:till_hour'] = 'Till Hour';
$lang['label:account'] = 'Account';
$lang['label:account_number'] = 'Account Number';
$lang['label:account_name'] = 'Account Name';
$lang['label:with_operator'] = 'With Operator';
$lang['label:service_group_discount'] = 'Service Group Discount';
$lang['label:service_group_component'] = 'Service Group Component';
$lang['label:total_discount'] = 'Total Discount';
$lang['label:indirect_discount'] = 'Indirect Discount';
$lang['label:with_service'] = 'With Service';
$lang['label:service_code'] = 'Service Code';
$lang['label:service_name'] = 'Service Name';
$lang['label:profit_center'] = 'Profit Center';
$lang['label:section_group'] = 'Section Group';
$lang['label:person_incharge'] = 'Person in Charge';
$lang['label:polyclinic'] = 'Polyclinic';
$lang['label:business_unit'] = 'Business Unit';
$lang['label:services'] = 'Services';
$lang['label:evidence_code'] = 'Evidence Code';
$lang['label:nrm_code'] = 'NRM Code';
$lang['label:pharmacy_ip'] = 'Pharmacy IP';
$lang['label:tt_amount'] = 'TT Amount';
$lang['label:in_mutation_account'] = 'IN Mutation Account';
$lang['label:out_mutation_account'] = 'OUT Mutation Account';
$lang['label:drug_revenue_account'] = 'Drug Revenue Account';
$lang['label:customer'] = 'Customer';
$lang['label:ri_only'] = 'RI Only';
$lang['label:baby_room'] = 'Baby Room';
$lang['label:icu'] = 'ICU';
$lang['label:block_co_payment'] = 'Block CO Payment';
$lang['label:order'] = 'Order';
$lang['label:room_number'] = 'Room Number';
$lang['label:sal'] = 'Sal';
$lang['label:bed_amount'] = 'Bed Amount';
$lang['label:floor_number'] = 'Floor Number';
$lang['label:added'] = 'Added';
$lang['label:bor'] = 'BOR';
$lang['label:description'] = 'Description';
$lang['label:start_date'] = 'Date Start';
$lang['label:evidence_number'] = 'Evidence Number';
$lang['label:note'] = 'Note';
$lang['label:status'] = 'Status';
$lang['label:created'] = 'Created';
$lang['label:updated'] = 'Updated';


$lang['select:type_no_select']        = 'Select a Type';
$lang['select:nationality_no_select'] = 'Select a Nationality';
$lang['select:country_no_select']    = 'Select a Country';
$lang['select:province_no_select']    = 'Select a Province';
$lang['select:couty_no_select']        = 'Select a County';
$lang['select:district_no_select']    = 'Select a District';
$lang['select:village_no_select']        = 'Select a Village';


//room
$lang['room:duplicate_bed']        = 'There is duplicate Bed data.';
$lang['room:bed_added']            = 'Bed successfully added';
$lang['room:bed_updated']            = 'Bed successfully updated';
