<?php
if (!defined('BASEPATH'))
	exit('No direct script access allowed');
?>

<?php echo form_open(site_url("{$nameroutes}/mass_action"), [
	'id' => 'form_crud__list',
	'name' => 'form_crud__list',
	'rule' => 'form',
	'class' => ''
]); ?>
<div class="row">
	<div class="col-md-12">
		<div class="panel panel-default">
			<div class="panel-heading">
				<div class="row">
					<div class="col-md-6">
						<h3 class="panel-title"><?php echo 'Daftar Master Status Kamar'; ?></h3>
					</div>
					<div class="col-md-6">
						<div class="panel-bars">
							<ul class="btn-bars">
								<li class="dropdown">
									<a data-toggle="dropdown" class="dropdown-toggle" href="javascript:;">
										<i class="fa fa-bars fa-lg tip" data-placement="left" title="<?php echo lang("actions") ?>"></i>
									</a>
									<ul class="dropdown-menu pull-right" role="menu">
										<li>
											<a data-action-url="<?php echo site_url("{$nameroutes}/create") ?>" data-act="ajax-modal"><i class="fa fa-plus"></i> <?php echo lang('action:add') ?></a>
										</li>
									</ul>
								</li>
							</ul>
						</div>
					</div>
				</div>
			</div>
			<div class="panel-body">
				<div class="row">
					<div class="col-md-12">
						<div class="form-room_status">
							<table id="dt_master_room_status" class="table table-bordered table-hover" width="100%" cellspacing="0">
								<thead>
									<tr>
										<th><?php echo 'Kode Status Kamar' ?></th>
										<th><?php echo 'Nama Status Kamar' ?></th>
										<th><?php echo 'Nama Status Kamar International' ?></th>
										<th class="text-center"><i class="fa fa-gear"></i></th>
									</tr>
								</thead>
								<tbody>
								</tbody>
								<tfoot class="dtFilter">
									<tr>
										<th><?php echo 'Kode Status Kamar' ?></th>
										<th><?php echo 'Nama Status Kamar' ?></th>
										<th><?php echo 'Nama Status Kamar International' ?></th>
										<th class="text-center"><i class="fa fa-gear"></i></th>
									</tr>
								</tfoot>
							</table>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<?php echo form_hidden('mass_action', ''); ?>
<?php echo form_close() ?>
<script>
	(function($) {

		$.fn.extend({
			DataTableInit: function() {

				var _this = this;
				//function code for custom search
				var _datatable = _this.DataTable({
					processing: true,
					serverSide: true,
					paginate: true,
					ordering: true,
					lengthMenu: [10, 30, 75],
					order: [
						[0, 'desc']
					],
					searching: true,
					info: true,
					responsive: true,
					ajax: {
						url: "<?php echo site_url("{$nameroutes}/datatable_collection") ?>",
						type: "POST",
						data: function(params) {}
					},
					columns: [{
							data: 'Kode_Status_Kamar',
							render: function(val, type, row) {
								return "<b>" + val + "</b>";
							}
						},
						{
							data: 'Nama_Status_Kamar'
						},
						{
							data: 'Nama_Status_Kamar_International'
						},
						{
							data: 'Kode_Status_Kamar',
							className: "text-center",
							orderable: false,
							width: "150px",
							render: function(val, type, row) {
								var buttons = "<div class=\"btn-group pull-center\" role=\"group\">";
								buttons += "<a data-action-url=\"<?php echo base_url("{$nameroutes}/update") ?>/" + val + "\" data-act=\"ajax-modal\" title=\"<?php echo lang('buttons:edit') ?>\" class=\"btn btn-info btn-xs\"> <i class=\"fa fa-edit\"></i></a>";
								buttons += "<a href=\"javascript:;\" data-action-url=\"<?php echo base_url("{$nameroutes}/delete") ?>/" + val + "\" data-act=\"ajax-modal\" data-title=\"<?php echo lang('buttons:delete') ?>\" title=\"<?php echo lang('buttons:delete') ?>\" class=\"btn btn-danger btn-xs\"> <i class=\"fa fa-trash\"></i></a>";
								buttons += "</div>";

								return buttons
							}
						}
					]
				});

				return _this;
			}

		});

		$(document).ready(function(e) {
			var _form = $('form[name="form_crud__list"]');
			_form.find("button[name=\"btn_search\"]").on("click", function(e) {
				$("#dt_master_room_status").DataTable().ajax.reload();
			});

			$("#dt_master_room_status").DataTableInit();

			$('.panel-bars .btn-bars .dropdown-menu a[data-mass="delete"]').click(function(e) {
				e.preventDefault();
				_form.find('input[name="mass_action"]').val($(this).attr('data-mass'));
				_form.trigger('submit');
			});
		});
	})(jQuery);
</script>