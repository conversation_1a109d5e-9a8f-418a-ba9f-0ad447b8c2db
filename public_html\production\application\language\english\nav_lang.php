<?php
defined('BASEPATH') or exit('No direct script access allowed');

$lang['nav_title:panel'] = 'Panel';
$lang['nav_title:system'] = 'System';
$lang['nav_title:restful'] = 'RESTful WS';

$lang['nav'] = 'Navigation';
$lang['nav:heading_panel'] = 'Panel';
$lang['nav:dashboard'] = 'Dashboard';
$lang['nav:servings'] = 'Servings';
$lang['nav:transaction'] = 'Transaction';
$lang['nav:transactions'] = 'Transactions';
$lang['nav:references'] = 'References';
$lang['nav:preferences'] = 'Preferences';
$lang['nav:equipments'] = 'Equipments';
$lang['nav:reports'] = 'Reports';
$lang['nav:setup'] = 'Setup';
$lang['nav:settings'] = 'Settings';
$lang['nav:common'] = 'Common Data';
$lang['nav:master'] = 'Master Data';
$lang['nav:system'] = 'System';
$lang['nav:users'] = 'Users';
$lang['nav:help'] = 'Help';
$lang['nav:support'] = 'Support';

$lang['nav:panel'] = 'Panel';
$lang['nav:dashboard'] = 'Dashboard';
$lang['nav:restful'] = 'RESTful';
$lang['nav:configs'] = 'Configs';
$lang['nav:users'] = 'Users';
$lang['nav:my_profile'] = 'My Profile';
$lang['nav:help'] = 'Help';
$lang['nav:ticket'] = 'Ticket';
$lang['nav:submit_ticket'] = 'Submit Ticket';
$lang['nav:signin'] = 'Sign In';
$lang['nav:login'] = 'Login';
$lang['nav:signout'] = 'Sign Out';
$lang['nav:logout'] = 'Logout';

$lang["restful:access"] = "Access";
$lang["restful:keys"] = "Keys";
$lang["restful:limits"] = "Limits";
$lang["restful:controllers"] = "Controllers";
$lang["restful:uris"] = "URIs";
$lang["restful:logs"] = "Logs Report";

$lang["nav:reports"] = "Reports";

// Registration
$lang['nav:reservation'] = 'Reservation';
$lang['nav:registration'] = 'Registration';
$lang['nav:transfer_inpatient'] = 'Transfer Inpatient';
$lang['nav:cashier'] = 'Cashier';
$lang['nav:drug_realization'] = 'Drug Realization';
$lang['nav:drug_payment'] = 'Drug Payment';
$lang['nav:return_drug_realization'] = 'Return Drug Realization';
$lang['nav:outstanding_payment'] = 'Outstanding Payment';
$lang['nav:petty_cash'] = 'Petty Cash';
$lang['nav:non_invoice_receipt'] = 'Non Invoice Receipt';
$lang['nav:non_invoice_cash_expense'] = 'Non Invoice Cash expense';
$lang['nav:bank_cash_deposit'] = 'Bank Cash Desposit';
$lang['nav:schedule'] = 'Schedule';
$lang['nav:patients'] = 'Patients';
$lang['nav:report_patient_reservations'] = 'Patient Reservations';
$lang['nav:report_polyclinic_registrations'] = 'Polyclinic Registrations';
$lang['nav:report_registration_amount_per_patient_type'] = 'Registration Patient Type';
$lang['nav:report_transaction_recap'] = 'Recap Transaction';
$lang['nav:report_transaction_recap_by_section_doctor'] = 'Recap Transaction Section &amp; Doctor';
$lang['nav:report_transaction_recap_by_service_group'] = 'Recap Transaction Section &amp; Doctor with Service Group';

// Pharmacy
$lang['nav:pharmacy'] = 'Pharmacy';
$lang['nav:selling'] = 'Selling';
$lang['nav:stock_report'] = 'Stock Report';
$lang['nav:report_warehouse_cards'] = 'Warehouse Cards';
$lang['nav:report_recap_stocks'] = 'Recap Stocks';
$lang['nav:report_stock_opname'] = 'Stock Opname';
$lang['nav:report_recap_transactions'] = 'Recap Transactions';
$lang['nav:report_total_sales'] = 'Total Sales';
$lang['nav:report_drug_sales'] = 'Drug Sales';
$lang['nav:report_drug_sale_patient_types'] = 'Drug Sales Per Patient Type';
$lang['nav:report_drug_sale_suppliers'] = 'Drug Sales Per Supplier';

$lang['nav:performance_report'] = 'Performance Report';
$lang['nav:unit_performance_report'] = 'Unit Performance Report';
$lang['nav:turnover_report'] = 'Turnover Report';
$lang['nav:patient_symptom_therapi'] = 'Laporan Patient Symptom Therapi ';

// Inquiry
$lang['nav:inquiry'] = 'Inquiry';
$lang['nav:mutation'] = 'Mutation';
$lang['nav:mutation_return'] = 'Mutation Return';
$lang['nav:stock_opname'] = 'Stock Opname';

// Poli
$lang['nav:outpatient'] = 'Outpatient';
$lang['nav:inpatient'] = 'Inpatient';
$lang['nav:laboratory'] = 'Lab';
$lang['nav:item_usage'] = 'Item Usage';
$lang['nav:examination'] = 'Examination';

// Inventory
$lang['nav:inventory'] = 'Inventory';
$lang['nav:products'] = 'Products';
$lang['nav:search_products'] = 'Search Products';
$lang['nav:submit_products'] = 'Submit a New Product';
$lang['nav:product_unit'] = 'Product Units';
$lang['nav:search_unit'] = 'Search Units';
$lang['nav:submit_unit'] = 'Submit a New Unit';
$lang['nav:product_group'] = 'Product Groups';
$lang['nav:search_group'] = 'Search Groups';
$lang['nav:submit_group'] = 'Submit a New Group';
$lang['nav:product_group_type'] = 'Product Group Types';
$lang['nav:search_group_type'] = 'Search Group Types';
$lang['nav:submit_group_type'] = 'Submit a New Group Type';
$lang['nav:product_category'] = 'Product Categories';
$lang['nav:search_category'] = 'Search Categories';
$lang['nav:submit_category'] = 'Submit a New Category';
$lang['nav:product_class'] = 'Product Classes';
$lang['nav:search_class'] = 'Search Class';
$lang['nav:submit_class'] = 'Submit a New Class';
$lang['nav:drugs'] = 'Drugs';

$lang['nav:stock'] = 'Stock';
$lang['nav:stock_in'] = 'Stock In';
$lang['nav:stock_out'] = 'Stock Out';

// Family Folder
$lang['nav:folder'] = 'Folder';
$lang['nav:family_folder'] = 'Family Folder';
$lang['nav:family_manage'] = 'Manage Family';
$lang['nav:personal'] = 'Personal';

// Inventory
$lang['nav:heading_inventory'] = 'Inventory';
$lang['nav:inv'] = 'Inventory';
$lang['nav:inventory'] = 'Inventory';
$lang['nav:transactions'] = 'Transactions';
$lang['nav:references'] = 'References';
$lang['nav:preferences'] = 'Preferences';
$lang['nav:behaviors'] = 'Behaviors';
$lang['nav:master'] = 'Master Data';
$lang['nav:setup'] = 'Setup';
$lang['nav:tools'] = 'Tools';
$lang['nav:reports'] = 'Reports';
$lang['nav:help'] = 'Help';

// Verifikasi
$lang['nav:heading_verification'] = 'Verification';
$lang['nav:verification'] = 'Verification';
$lang['nav:revenue_recognition'] = 'Revenue Recognition';

// Back Office
$lang['nav:back_office'] = 'Back Office';
$lang['nav:accounting'] = 'Accounting';
$lang['nav:accounts'] = 'Accounts Master';
$lang['nav:search_accounts'] = 'Search Accounts';
$lang['nav:tree_accounts'] = 'Tree View Accounts';
$lang['nav:submit_account'] = 'Submit a New Account';
$lang['nav:account_concept'] = 'Account Concept';
$lang['nav:account_structure'] = 'Account Structure';
$lang['nav:account_income_loss'] = 'Account Income Loss';

$lang['nav:general_ledger'] = 'General Ledger';
$lang['nav:ledger'] = 'Ledger';
$lang['nav:journal_transaction'] = 'Journal Transactions';
$lang['nav:view_journal'] = 'View Jurnal';
$lang['nav:search_journal_transactions'] = 'Search Journal Transactions';
$lang['nav:submit_journal_transaction'] = 'Submit a New Journal Transactions';
$lang['nav:beginning_balances'] = 'Beginning Balances System';
$lang['nav:close_books'] = 'Close Book';
$lang['nav:cancel_close_books'] = 'Cancel Close Book';
$lang['nav:balance_sheets'] = 'Ballance Sheets';
$lang['nav:trial_balance'] = 'Trial Balance';
$lang['nav:income_loss'] = 'Income Loss';
$lang['nav:income_loss_quarterly'] = 'Income Loss Quartery';
$lang['nav:income_loss_annual'] = 'Income Loss Annual';
$lang['nav:income_loss_setup'] = 'Income Loss Setup';
$lang['nav:cash_flow'] = 'Cash Flow';
$lang['nav:cash_flow_setup'] = 'Cash Flow Setup';
$lang['nav:cash_flow_account'] = 'Cash Flow Account';
$lang['nav:cash_flow_report'] = 'Cash Flow Report';

$lang['nav:payable'] = 'Payable';
$lang['nav:payable_types'] = 'Payable Types';
$lang['nav:search_payable_types'] = 'Search Payable Types';
$lang['nav:submit_payable_types'] = 'Submit a New Payable Type';
$lang['nav:payable_factur'] = 'Factur';
$lang['nav:payable_voucher'] = 'Voucher';
$lang['nav:payable_credit_debit_notes'] = 'Credit Debit Notes';
$lang['nav:payable_posting'] = 'Posting';
$lang['nav:payable_posting_cancel'] = 'Cancel Posting';
$lang['nav:payable_close_book'] = 'Close Book';
$lang['nav:payable_aging'] = 'Payable Aging ';
$lang['nav:payable_report'] = 'Report';
$lang['nav:payable_report_card'] = 'Payable Card';
$lang['nav:payable_report_recap'] = 'Payable Recap';

$lang['nav:receivable'] = 'Receivable';
$lang['nav:receivable_types'] = 'Receivable Tipes';
$lang['nav:search_receivable_types'] = 'Search Receivable Types';
$lang['nav:submit_receivable_type'] = 'Submit a New Receivable Types';
$lang['nav:receivable_factur'] = 'Factur';
$lang['nav:receivable_invoice'] = 'Invoice';
$lang['nav:receivable_credit_debit_notes'] = 'Credit Debit Notes';
$lang['nav:receivable_posting'] = 'Posting';
$lang['nav:receivable_posting_cancel'] = 'Cancel Posting';
$lang['nav:receivable_close_book'] = 'Close Book';
$lang['nav:receivable_report'] = 'Report';
$lang['nav:receivable_aging'] = 'Receivable Aging ';
$lang['nav:receivable_report_card'] = 'Receivable Card';
$lang['nav:receivable_report_recap'] = 'Receivable Recap';

$lang['nav:general_cashier'] = 'General Cashier';
$lang['nav:cash_bank_income'] = 'Cash Bank Income';
$lang['nav:cash_bank_income_invoice'] = 'Cash Bank Income Invoice';
$lang['nav:cash_bank_income_non_invoice'] = 'Cash Bank Income Non Invoice';
$lang['nav:cash_bank_expense'] = 'Cash Bank Expense';
$lang['nav:cash_bank_expense_voucher'] = 'Cash Bank Expense Voucher';
$lang['nav:cash_bank_expense_non_voucher'] = 'Cash Bank Expense Non Voucher';
$lang['nav:cash_bank_mutation'] = 'Cash Bank Mutation';
$lang['nav:general_cashier_posting'] = 'Posting';
$lang['nav:general_cashier_posting_cancel'] = 'Cancel Posting';
$lang['nav:general_cashier_report'] = 'Report';
$lang['nav:general_cashier_report_cash'] = 'Cash';
$lang['nav:general_cashier_report_bank'] = 'Bank';
$lang['nav:general_cashier_report_account'] = 'Account';


//Admin
$lang['nav:admin'] = 'Admin';
$lang['nav:integration_insurance'] = 'Integration Insurance';
$lang['nav:insurance'] = 'Insurance';
$lang['nav:bpjs_pcare'] = 'BPJS Pcare';


//Assesmen
$lang['nav:beginning_assesment'] = 'Beginning Assesment';
