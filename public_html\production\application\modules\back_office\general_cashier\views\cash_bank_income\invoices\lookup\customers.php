<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');
?>

<div class="modal-dialog modal-lg" id="lookup-customers">
    <div class="modal-content">
        <div class="modal-header"> 
            <button type="button" class="close" data-dismiss="modal">&times;</button> 
            <h4 class="modal-title"><?php echo lang('invoices:customer_lookup_title') ?></h4>
        </div>
        <div class="modal-body">
        	<script type="text/javascript">//<![CDATA[
			function lookupbox_row_selected( response ){
				var _response = JSON.parse(response)
				if( _response ){
					
					try{
					
						$("#Customer_ID").val( _response.Customer_ID );
						$("#Kode_Customer").val( _response.Kode_Customer );
						$("#Nama_Customer").val( _response.Nama_Customer );
						
						$("#dt_invoice_details").DataTable().clear().draw();
							
					}catch(e){ console.log(e)}

					$( '#lookup-customers' ).parent().remove();
					$("body").removeClass("modal-open");
					
				}
			}
			//]]></script>
            <?php  echo Modules::run( "common/customers/lookup_back_office", true ) ?>
        </div>
        <div class="modal-footer">
        	<?php echo lang('customer:customer_lookup_helper') ?>
        </div>
    </div>
	<!-- /.modal-content -->
</div>
<!-- /.modal-dialog -->

