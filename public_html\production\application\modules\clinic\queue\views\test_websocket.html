<!DOCTYPE html>
<html>
<head>
    <title>Test WebSocket Queue</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .test-section h3 { margin-top: 0; color: #333; }
        button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .log { background: #f8f9fa; padding: 10px; border-radius: 3px; margin: 10px 0; font-family: monospace; font-size: 12px; max-height: 200px; overflow-y: auto; }
        .error { color: #dc3545; }
        .success { color: #28a745; }
        .warning { color: #ffc107; }
        input[type="text"] { padding: 8px; margin: 5px; border: 1px solid #ddd; border-radius: 3px; width: 200px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test WebSocket Queue System</h1>
        
        <div class="test-section">
            <h3>📡 WebSocket Connection</h3>
            <button onclick="connectWebSocket()">Connect</button>
            <button onclick="disconnectWebSocket()">Disconnect</button>
            <span id="connection-status">❌ Disconnected</span>
        </div>

        <div class="test-section">
            <h3>📤 Test Data Formats</h3>
            
            <h4>1. JSON Object Format (Recommended)</h4>
            <button onclick="sendJSONObject()">Send JSON Object</button>
            <pre>{"action": "queue_calling", "NoReg": "REG001", "SectionID": "SECT001", "Ruangan": 1}</pre>
            
            <h4>2. JSON Array Format (Legacy)</h4>
            <button onclick="sendJSONArray()">Send JSON Array</button>
            <pre>["queue_calling_poli", "REG002", "SECT002", "2"]</pre>
            
            <h4>3. String CSV Format (Legacy)</h4>
            <button onclick="sendStringCSV()">Send String CSV</button>
            <pre>"queue_calling_poli,REG003,SECT003,3"</pre>
            
            <h4>4. Invalid Format (Should Fail)</h4>
            <button onclick="sendInvalidData()">Send Invalid Data</button>
            <pre>"queue_call_invalid_format"</pre>
        </div>

        <div class="test-section">
            <h3>🎛️ Custom Test</h3>
            <input type="text" id="custom-noreg" placeholder="NoReg (e.g., REG004)" value="REG004">
            <input type="text" id="custom-section" placeholder="SectionID (e.g., SECT004)" value="SECT004">
            <input type="text" id="custom-ruangan" placeholder="Ruangan (e.g., 4)" value="4">
            <br>
            <button onclick="sendCustomJSON()">Send Custom JSON</button>
            <button onclick="sendCustomArray()">Send Custom Array</button>
            <button onclick="sendCustomString()">Send Custom String</button>
        </div>

        <div class="test-section">
            <h3>📋 Console Log</h3>
            <button onclick="clearLog()">Clear Log</button>
            <div id="log" class="log"></div>
        </div>
    </div>

    <script>
        let socket = null;
        const wsUrl = 'wss://*************'; // Ganti dengan IP WebSocket server Anda

        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : '';
            logDiv.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }

        function connectWebSocket() {
            if (socket && socket.readyState === WebSocket.OPEN) {
                log('WebSocket sudah terhubung', 'warning');
                return;
            }

            socket = new WebSocket(wsUrl);
            
            socket.onopen = () => {
                document.getElementById('connection-status').innerHTML = '✅ Connected';
                log('WebSocket terhubung ke ' + wsUrl, 'success');
            };

            socket.onclose = () => {
                document.getElementById('connection-status').innerHTML = '❌ Disconnected';
                log('WebSocket terputus', 'warning');
            };

            socket.onerror = (error) => {
                log('WebSocket error: ' + error, 'error');
            };

            socket.onmessage = (e) => {
                log('📨 Pesan diterima: ' + e.data, 'success');
            };
        }

        function disconnectWebSocket() {
            if (socket) {
                socket.close();
                socket = null;
            }
        }

        function sendData(data) {
            if (!socket || socket.readyState !== WebSocket.OPEN) {
                log('❌ WebSocket tidak terhubung!', 'error');
                return;
            }
            
            log('📤 Mengirim: ' + data);
            socket.send(data);
        }

        function sendJSONObject() {
            const data = JSON.stringify({
                action: "queue_calling",
                NoReg: "REG001",
                SectionID: "SECT001",
                Ruangan: 1
            });
            sendData(data);
        }

        function sendJSONArray() {
            const data = JSON.stringify(["queue_calling_poli", "REG002", "SECT002", "2"]);
            sendData(data);
        }

        function sendStringCSV() {
            const data = "queue_calling_poli,REG003,SECT003,3";
            sendData(data);
        }

        function sendInvalidData() {
            const data = "queue_call_invalid_format";
            sendData(data);
        }

        function sendCustomJSON() {
            const noreg = document.getElementById('custom-noreg').value;
            const section = document.getElementById('custom-section').value;
            const ruangan = document.getElementById('custom-ruangan').value;
            
            const data = JSON.stringify({
                action: "queue_calling",
                NoReg: noreg,
                SectionID: section,
                Ruangan: parseInt(ruangan) || 99
            });
            sendData(data);
        }

        function sendCustomArray() {
            const noreg = document.getElementById('custom-noreg').value;
            const section = document.getElementById('custom-section').value;
            const ruangan = document.getElementById('custom-ruangan').value;
            
            const data = JSON.stringify(["queue_calling_poli", noreg, section, ruangan]);
            sendData(data);
        }

        function sendCustomString() {
            const noreg = document.getElementById('custom-noreg').value;
            const section = document.getElementById('custom-section').value;
            const ruangan = document.getElementById('custom-ruangan').value;
            
            const data = `queue_calling_poli,${noreg},${section},${ruangan}`;
            sendData(data);
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        // Auto connect on page load
        window.onload = () => {
            log('🚀 Halaman test dimuat. Klik "Connect" untuk memulai.');
        };
    </script>
</body>
</html>
