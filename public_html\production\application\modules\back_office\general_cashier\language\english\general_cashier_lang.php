<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

$lang['general_cashier:page']		= "General Cashier";

/* TYPES 
-------------------------------------------------------------------------------*/
// titles
$lang['types:page']           			= 'Payable Types';
$lang['types:breadcrumb']           	= 'Payable Types';
$lang['types:modal_heading']        	= 'Details';
$lang['types:list_heading']       		= 'List of Payable Types';
$lang['types:create_heading']       	= 'Create a New Payable Type';
$lang['types:edit_heading']         	= 'Editing Payable Type';

// labels
$lang['types:code_label']				= 'Code';
$lang['types:type_label']				= 'Type Name';
$lang['types:account_label']			= 'Account';
$lang['types:description_label']		= 'Description';
$lang['types:state_label']				= 'Status';
$lang['types:updated_label']			= 'Updated';

/* Facturs
-------------------------------------------------------------------------------*/
// titles
$lang['general_cashier:page']           			= 'General Cashier';
$lang['general_cashier:breadcrumb']        	 		= 'General Cashier';
$lang['general_cashier:modal_heading']     		  	= 'Details Factur';
$lang['general_cashier:create_heading']      		= 'General Cashier';
$lang['general_cashier:edit_heading']        		= 'Editing General Cashier';
$lang['general_cashier:widget_heading']      		= 'List of General Cashier';

$lang['general_cashier:cancel_title']      			= 'Cancel Factur';
$lang['general_cashier:form_invoice_title']      	= 'Lookup Form Invoice';
$lang['general_cashier:form_vouhcer_title']      	= 'Lookup Form Vouhcer';
$lang['general_cashier:lookup_invoice_title']      	= 'Lookup List Invoice';
$lang['general_cashier:lookup_vouhcer_title']      	= 'Lookup List Vouhcer';

//subtitles
$lang['general_cashier:accounts_details_heading']   = 'Accounts Details';
$lang['general_cashier:transaction_debit_sub']      = 'Transaction Debit';
$lang['general_cashier:transaction_credit_sub']     = 'Transaction Credit';

// labels
$lang['general_cashier:evidence_number_label']		= 'Evidence Number';
$lang['general_cashier:voucher_number_label']		= 'Voucher Number';
$lang['general_cashier:invoice_number_label']		= 'Invoice Number';
$lang['general_cashier:date_label']					= 'Date';
$lang['general_cashier:transaction_type_label']	= 'Transaction Type';
$lang['general_cashier:description_label']			= 'Description';
$lang['general_cashier:from_to_label']				= 'From/To';
$lang['general_cashier:debit_label']				= 'Debit';
$lang['general_cashier:credit_label']				= 'Credit';
$lang['general_cashier:account_number_label']		= 'Account Number';
$lang['general_cashier:account_name_label']			= 'Account Name';
$lang['general_cashier:normal_pos_label']			= 'Normal Pos';
$lang['general_cashier:original_value_label']		= 'Original Value';
$lang['general_cashier:value_label']				= 'Value';
$lang['general_cashier:remain_label']				= 'Remain';
$lang['general_cashier:qty_label']					= 'Qty';
$lang['general_cashier:supplier_label']				= 'Supplier';
$lang['general_cashier:customer_label']				= 'Customer';

$lang['general_cashier:periode_label']				= 'Periode';
$lang['general_cashier:till_label']					= 'Till';
$lang['general_cashier:cancel_general_cashier_label']	= 'Cancel Factur';
$lang['general_cashier:find_general_cashier_list_label']= 'Find Factur List';

$lang['general_cashier:close_book_data']			= 'This data is already Close Book!';
$lang['general_cashier:posted_data']				= 'This data is already Posted!';
$lang['general_cashier:has_credit_debit_notes']		= 'Cancel Factur failed! This Factur has Credit Debit Notes!';

$lang['general_cashier:value_not_match']			= 'Factur value cannot be 0 or minus!';
$lang['general_cashier:details_cannot_empty']		= 'Details account cannot empty!';
$lang['general_cashier:cancel_general_cashier']				= 'Are you sure to cancel this general_cashier ?';
$lang['general_cashier:cannot_delete_close_data']	= 'Cannot cancel close book data!';

/*  CASH BANK INCOME
-------------------------------------------------------------------------------*/
// titles
$lang['cash_bank_income:page']          = 'Cash Bank Income';
$lang['cash_bank_income:breadcrumb']    = 'Cash Bank Income';

$lang['cash_bank_income:invoice_page']          = 'Cash Bank Income Invoices';
$lang['cash_bank_income:invoice_breadcrumb']    = 'Invoices';
$lang['cash_bank_income:invoice_modal_heading'] = 'Details';
$lang['cash_bank_income:invoice_list_heading']  = 'List of Cash Bank Income Invoices';
$lang['cash_bank_income:invoice_create_heading']= 'Create a New Cash Bank Income Invoice';
$lang['cash_bank_income:invoice_edit_heading']	= 'Editing Cash Bank Income Invoice';
$lang['cash_bank_income:invoice_cancel_title']  = 'Cancel Cash Bank Income Invoice';

$lang['cash_bank_income:non_invoice_page']          = 'Non Invoices';
$lang['cash_bank_income:non_invoice_breadcrumb']    = 'Non Invoices';
$lang['cash_bank_income:non_invoice_modal_heading'] = 'Details';
$lang['cash_bank_income:non_invoice_list_heading']  = 'List of Cash Bank Income Non Invoices';
$lang['cash_bank_income:non_invoice_create_heading']= 'Create a New Cash Bank Income Non Invoice';
$lang['cash_bank_income:non_invoice_edit_heading']  = 'Editing Cash Bank Income Non Invoice';
$lang['cash_bank_income:non_invoice_cancel_title']  = 'Cancel Cash Bank Income Non Invoice';

// subtitle
$lang['cash_bank_income:invoice_detail_subtitle'] 	= "Invoice Details";
$lang['cash_bank_income:account_detail_subtitle'] 	= "Account Details";

// labels
$lang['cash_bank_income:evidence_number_label']	= 'Evidence Number';
$lang['cash_bank_income:invoice_number_label']	= 'Invoice Number';
$lang['cash_bank_income:factur_number_label']	= 'Factur Number';
$lang['cash_bank_income:customer_label']		= 'Customer';
$lang['cash_bank_income:date_label']			= 'Date';
$lang['cash_bank_income:due_date_label']		= 'Due Date';
$lang['cash_bank_income:from_label']			= 'From';
$lang['cash_bank_income:value_label']			= 'Value';
$lang['cash_bank_income:remain_label']			= 'Remain';
$lang['cash_bank_income:pay_label']				= 'Pay';
$lang['cash_bank_income:balance_label']			= 'Balance';
$lang['cash_bank_income:description_label']		= 'Description';
$lang['cash_bank_income:account_label']			= 'Account';
$lang['cash_bank_income:account_number_label']	= 'Account Number';
$lang['cash_bank_income:account_name_label']	= 'Account Name';
$lang['cash_bank_income:debit_label']			= 'Debit';
$lang['cash_bank_income:credit_label']			= 'Credit';
$lang['cash_bank_income:type_label']			= 'Tipe';
$lang['cash_bank_income:section_label']			= 'Section';
$lang['cash_bank_income:state_label']			= 'State';
$lang['cash_bank_income:updated_label']			= 'Updated';
$lang['cash_bank_income:mutation_total_label']	= 'Mutation Total';
$lang['cash_bank_income:mutation_remain_label']	= 'Mutation Remain';
$lang['cash_bank_income:username_label']		= 'Username';
$lang['cash_bank_income:password_label']		= 'Password';

$lang['cash_bank_income:periode_label']			= 'Periode';
$lang['cash_bank_income:till_label']			= 'Till';
$lang['cash_bank_income:view_label']			= 'View';
$lang['cash_bank_income:cancel_invoice_label']	= 'Cancel Invoice';
$lang['cash_bank_income:find_invoice_list_label']= 'Find Invoice List';
$lang['cash_bank_income:find_account_list_label']= 'Find Account List';
$lang['cash_bank_income:invoice_lookup_title'] 	= 'Lookup Data Invoice';
$lang['cash_bank_income:customer_lookup_title'] = 'Lookup Data Customer';
$lang['cash_bank_income:factur_lookup_title'] 	= 'Lookup Data Factur';

$lang['cash_bank_income:close_book_data']			= 'This data is already Close Book!';
$lang['cash_bank_income:posted_data']				= 'This data is already Posted!';
$lang['cash_bank_income:cancel_data']				= 'This data is already Cancelled!';
$lang['cash_bank_income:cancel_confirm']			= 'Are you sure to Cancel this Transaction?';

$lang['cash_bank_income:customer_not_selected']		= 'Customer not selected! \n Please select the Customer first.';
$lang['cash_bank_income:factur_already_selected']	= 'This Factur: %s is already selected!';
$lang['cash_bank_income:details_cannot_empty']		= 'The transaction can not continue. Because Detail Data not selected!';
$lang['cash_bank_income:transaction_date_incorret']	= 'Transaction Date (%s) must not be before Invoice Date (%s)!';
$lang['cash_bank_income:empty_pay_value']			= 'The transaction can not be continued, Because the Pay value has not been inputted in Invoice: %s!';
$lang['cash_bank_income:empty_account_value']		= 'The transaction can not be continued, Because the Value has not been inputted in Account: %s!';
$lang['cash_bank_income:pay_value_exceed']			= 'The transaction can not proceed, Because the Pay Value exceeds the Remain Value in Invoice: %s!';
$lang['cash_bank_income:receivable_already_closing_period']	= 'The transaction can not continue. Because there is already Closing in Receivable Application on period: %s';
$lang['cash_bank_income:general_ledger_already_closing_period']	= 'The transaction can not continue. Because there is already Closing in General Ledger Application on period: %s';
$lang['cash_bank_income:reconciliation_data']		= 'The transaction can not proceed because No. The proof of the transaction is already in RECONCILIATION!';
$lang['cash_bank_income:cancel_invoice']			= 'Are you sure to cancel this Invoice ?';

/*  CASH BANK EXPENSE
-------------------------------------------------------------------------------*/
// titles
$lang['cash_bank_expense:page']          = 'Cash Bank Income';
$lang['cash_bank_expense:breadcrumb']    = 'Cash Bank Income';

$lang['cash_bank_expense:voucher_page']          	= 'Vouchers';
$lang['cash_bank_expense:voucher_breadcrumb']    	= 'Vouchers';
$lang['cash_bank_expense:voucher_modal_heading'] 	= 'Details';
$lang['cash_bank_expense:voucher_list_heading']  	= 'List of Cash Bank Expense Vouchers';
$lang['cash_bank_expense:voucher_create_heading']	= 'Create a New Cash Bank Expense Voucher';
$lang['cash_bank_expense:voucher_edit_heading']		= 'Editing Cash Bank Expense Voucher';
$lang['cash_bank_expense:voucher_cancel_title']  	= 'Cancel Cash Bank Expense Voucher';


// subtitle
$lang['cash_bank_expense:voucher_detail_subtitle'] 	= "Voucher Details";
$lang['cash_bank_expense:account_detail_subtitle'] 	= "Account Details";

// labels
$lang['cash_bank_expense:evidence_number_label']= 'Evidence Number';
$lang['cash_bank_expense:voucher_number_label']	= 'Voucher Number';
$lang['cash_bank_expense:factur_number_label']	= 'Factur Number';
$lang['cash_bank_expense:customer_label']		= 'Customer';
$lang['cash_bank_expense:date_label']			= 'Date';
$lang['cash_bank_expense:due_date_label']		= 'Due Date';
$lang['cash_bank_expense:from_label']			= 'From';
$lang['cash_bank_expense:value_label']			= 'Value';
$lang['cash_bank_expense:remain_label']			= 'Remain';
$lang['cash_bank_expense:pay_label']			= 'Pay';
$lang['cash_bank_expense:balance_label']		= 'Balance';
$lang['cash_bank_expense:description_label']	= 'Description';
$lang['cash_bank_expense:account_label']		= 'Account';
$lang['cash_bank_expense:account_number_label']	= 'Account Number';
$lang['cash_bank_expense:account_name_label']	= 'Account Name';
$lang['cash_bank_expense:debit_label']			= 'Debit';
$lang['cash_bank_expense:credit_label']			= 'Credit';
$lang['cash_bank_expense:type_label']			= 'Tipe';
$lang['cash_bank_expense:section_label']		= 'Section';
$lang['cash_bank_expense:state_label']			= 'State';
$lang['cash_bank_expense:updated_label']		= 'Updated';
$lang['cash_bank_expense:mutation_total_label']	= 'Mutation Total';
$lang['cash_bank_expense:mutation_remain_label']= 'Mutation Remain';
$lang['cash_bank_expense:username_label']		= 'Username';
$lang['cash_bank_expense:password_label']		= 'Password';

$lang['cash_bank_expense:periode_label']		= 'Periode';
$lang['cash_bank_expense:till_label']			= 'Till';
$lang['cash_bank_expense:view_label']			= 'View';
$lang['cash_bank_expense:cancel_voucher_label']	= 'Cancel Voucher';
$lang['cash_bank_expense:find_voucher_list_label']= 'Find Voucher List';
$lang['cash_bank_expense:find_account_list_label']= 'Find Account List';
$lang['cash_bank_expense:voucher_lookup_title'] = 'Lookup Data Voucher';
$lang['cash_bank_expense:customer_lookup_title']= 'Lookup Data Customer';
$lang['cash_bank_expense:factur_lookup_title'] 	= 'Lookup Data Factur';

$lang['cash_bank_expense:close_book_data']		= 'This data is already Close Book!';
$lang['cash_bank_expense:posted_data']			= 'This data is already Posted!';
$lang['cash_bank_expense:cancel_data']			= 'This data is already Cancelled!';
$lang['cash_bank_expense:cancel_confirm']		= 'Are you sure to Cancel this Transaction?';

$lang['cash_bank_expense:customer_not_selected']= 'Customer not selected! \n Please select the Customer first.';
$lang['cash_bank_expense:factur_already_selected']	= 'This Factur: %s is already selected!';
$lang['cash_bank_expense:details_cannot_empty']	= 'The transaction can not continue. Because Detail Data not selected!';
$lang['cash_bank_expense:transaction_date_incorret']	= 'Transaction Date (%s) must not be before Voucher Date (%s)!';
$lang['cash_bank_expense:empty_pay_value']		= 'The transaction can not be continued, Because the Pay value has not been inputted in Voucher: %s!';
$lang['cash_bank_expense:empty_account_value']	= 'The transaction can not be continued, Because the Value has not been inputted in Account: %s!';
$lang['cash_bank_expense:pay_value_exceed']		= 'The transaction can not proceed, Because the Pay Value exceeds the Remain Value in Voucher: %s!';
$lang['cash_bank_expense:payable_already_closing_period']	= 'The transaction can not continue. Because there is already Closing in Payable Application on period: %s';
$lang['cash_bank_expense:general_ledger_already_closing_period']	= 'The transaction can not continue. Because there is already Closing in General Ledger Application on period: %s';
$lang['cash_bank_expense:reconciliation_data']	= 'The transaction can not proceed because No. The proof of the transaction is already in RECONCILIATION!';
$lang['cash_bank_expense:cancel_voucher']		= 'Are you sure to cancel this Voucher ?';

/*  CASH BANK MUTATION
-------------------------------------------------------------------------------*/
// titles
$lang['cash_bank_mutation:page']          = 'Cash Bank Income';
$lang['cash_bank_mutation:breadcrumb']    = 'Cash Bank Income';

$lang['cash_bank_mutation:page']          	= 'Cash Bank Mutation';
$lang['cash_bank_mutation:breadcrumb']    	= 'Cash Bank Mutation';
$lang['cash_bank_mutation:modal_heading'] 	= 'Details';
$lang['cash_bank_mutation:list_heading']  	= 'List of Cash Bank Mutation';
$lang['cash_bank_mutation:create_heading']	= 'Create a New Cash Bank Mutation';
$lang['cash_bank_mutation:edit_heading']	= 'Editing Cash Bank Mutation';
$lang['cash_bank_mutation:cancel_title']  	= 'Cancel Cash Bank Mutation';

// subtitle
$lang['cash_bank_mutation:account_origin_subtitle'] 	= "Account Origin";
$lang['cash_bank_mutation:account_destination_subtitle']	= "Account Destination";

// labels
$lang['cash_bank_mutation:evidence_number_label']= 'Evidence Number';
$lang['cash_bank_mutation:date_label']			= 'Date';
$lang['cash_bank_mutation:from_label']			= 'From';
$lang['cash_bank_mutation:value_label']			= 'Value';
$lang['cash_bank_mutation:balance_label']		= 'Balance';
$lang['cash_bank_mutation:description_label']	= 'Description';
$lang['cash_bank_mutation:account_label']		= 'Account';
$lang['cash_bank_mutation:account_number_label']	= 'Account Number';
$lang['cash_bank_mutation:account_name_label']	= 'Account Name';
$lang['cash_bank_mutation:debit_label']			= 'Debit';
$lang['cash_bank_mutation:credit_label']		= 'Credit';
$lang['cash_bank_mutation:transfer_form_label']	= 'Transfer Form';
$lang['cash_bank_mutation:mutation_value_label']= 'Mutation Value';
$lang['cash_bank_mutation:type_label']			= 'Tipe';
$lang['cash_bank_mutation:mutation_total_label']	= 'Mutation Total';
$lang['cash_bank_mutation:mutation_remain_label']= 'Mutation Remain';
$lang['cash_bank_mutation:username_label']		= 'Username';
$lang['cash_bank_mutation:password_label']		= 'Password';

$lang['cash_bank_mutation:periode_label']		= 'Periode';
$lang['cash_bank_mutation:till_label']			= 'Till';
$lang['cash_bank_mutation:view_label']			= 'View';
$lang['cash_bank_mutation:find_account_list_label']= 'Find Account List';

$lang['cash_bank_mutation:posted_data']			= 'This data is already Posted!';
$lang['cash_bank_mutation:cancel_data']			= 'This data is already Cancelled!';
$lang['cash_bank_mutation:cancel_confirm']		= 'Are you sure to Cancel this Transaction?';

$lang['cash_bank_mutation:empty_account_value']	= 'The transaction can not be continued, Because the Value has not been inputted in Account: %s!';
$lang['cash_bank_mutation:general_ledger_already_closing_period']	= 'The transaction can not continue. Because there is already Closing in General Ledger Application on period: %s';
$lang['cash_bank_mutation:reconciliation_data']	= 'The transaction can not proceed because No. The proof of the transaction is already in RECONCILIATION!';

/* Beginning Balance
-------------------------------------------------------------------------------*/
// titles
$lang['beginning_balances:page']           				= 'Beginning Balances Payable';
$lang['beginning_balances:list_heading']       			= 'List of Beginning Balances Payable ';
$lang['beginning_balances:breadcrumb']        	 		= 'Beginning Balances Payable';
$lang['beginning_balances:modal_heading']     		  	= 'Details';
$lang['beginning_balances:create_heading']      		= 'Beginning Balances Payable';

//subtitles
$lang['beginning_balances:total_summary_sub']      		= 'Total Summary';

// labels
$lang['beginning_balances:date_label']					= 'Date';
$lang['beginning_balances:currency_label']				= 'Currency';
$lang['beginning_balances:supplier_label']				= 'Supplier';
$lang['beginning_balances:supplier_code_label']			= 'Supplier Code';
$lang['beginning_balances:supplier_name_label']			= 'Supplier Name';
$lang['beginning_balances:value_label']					= 'Value';

$lang['beginning_balances:paid_payable']				= 'This Payable is already paid! please cancel the payment if you want continue this proses!';
$lang['beginning_balances:close_book']					= 'This Payable is already Close Book! please cancel close book if you want continue this proses!';
$lang['beginning_balances:identical_posted']			= 'This Supplier and Type is Already in entry. Please entry other data!';

/* Postings
-------------------------------------------------------------------------------*/
// titles
$lang['postings:page']           			= 'Posting General Cashier';
$lang['postings:cancel_page']           	= 'Cancel Posting General Cashier';
$lang['postings:breadcrumb']        	 	= 'Posting General Cashier';
$lang['postings:modal_heading']     		= 'Details General Cashier';
$lang['postings:create_heading']      		= 'Posting General Cashier';
$lang['postings:widget_heading']      		= 'List of General Cashier';

$lang['postings:cancel_title']      		= 'Cancel Posting';

//subtitles
$lang['postings:accounts_details_heading']  = 'Accounts Details';
$lang['postings:total_summary_sub']      	= 'Total Summary';

// labels
$lang['postings:evidence_number_label']		= 'Evidence Number';
$lang['postings:voucher_number_label']		= 'Voucher Number';
$lang['postings:date_label']				= 'Date';
$lang['postings:due_date_label']			= 'Due Date';
$lang['postings:type_transaction_label']	= 'Type Transaction';
$lang['postings:description_label']			= 'Description';
$lang['postings:currency_label']			= 'Currency';
$lang['postings:section_label']				= 'Section';
$lang['postings:account_number_label']		= 'Account Number';
$lang['postings:account_name_label']		= 'Account Name';
$lang['postings:normal_pos_label']			= 'Normal Pos';
$lang['postings:value_label']				= 'Value';
$lang['postings:remain_label']				= 'Remain';
$lang['postings:qty_label']					= 'Qty';
$lang['postings:supplier_label']			= 'Supplier';
$lang['postings:username_label']			= 'Username';
$lang['postings:password_label']			= 'Password';

$lang['postings:periode_label']				= 'Periode';
$lang['postings:till_label']				= 'Till';
$lang['postings:cancel_posting_label']		= 'Cancel Postings';
$lang['postings:find_posting_list_label']	= 'Find Data';

$lang['postings:posting_successfully']		= 'Posting data successfull!';
$lang['postings:cancel_posting_successfully']= 'Cancel Posting data successfully!';
$lang['postings:value_not_match']			= 'Factur value cannot be 0 or minus!';
$lang['postings:no_data_selected']			= 'No data selected. Please select at least one data!';
$lang['postings:details_cannot_empty']		= 'Details account cannot empty!';
$lang['postings:posting_confirm']			= 'Are you sure to posting this data?';
$lang['postings:cancel_posting_confirm']	= 'Are you sure to cancel this posting data?';
$lang['postings:cannot_delete_close_data']	= 'Cannot cancel close book data!';
$lang['postings:posting_failed']			= 'Error Occurred! Failed posting data!';
$lang['postings:empty_posting_data']		= 'The transaction can not be continued, Because no Posting data is selected!';
$lang['postings:journal_close_book']		= 'Failed Cancel Posting! Evidence Number <b>%s</b> already closed book in Journal GL!';

/* Reports
-------------------------------------------------------------------------------*/
// titles
$lang['reports:page']           		= 'Reports Payable';
$lang['reports:breadcrumb']        	 	= 'Reports Payable';
$lang['reports:modal_heading']     		= 'Reports Note';
$lang['reports:create_heading']      	= 'Reports Payable';
$lang['reports:edit_heading']        	= 'Reports Payable';
$lang['reports:widget_heading']      	= 'Reports Payable';

//subtitles
$lang['reports:report_type_sub']     	= 'Select Report Type ';

//label
$lang['reports:report_card_payable_label']	= 'Payable Card Report';
$lang['reports:report_recap_payable_label']	= 'Payable Recap Report';

$lang['reports:periode_label']			= 'Periode';
$lang['reports:supplier_label']			= 'Supplier';
$lang['reports:periode_label']			= 'Periode';
$lang['reports:till_label']				= 'Till';

$lang['reports:number_label'] 			= 'No.';
$lang['reports:date_label'] 			= 'Date';
$lang['reports:evidence_number_label'] 	= 'Evidance Number';
$lang['reports:description_label'] 		= 'Description';
$lang['reports:beginning_balance_label']= 'Beginning Balance';
$lang['reports:debit_label'] 			= 'Debit';
$lang['reports:credit_label'] 			= 'Credit';
$lang['reports:ending_balance_label'] 	= 'Ending Balance';
$lang['reports:sub_total_label'] 		= 'Sub Total';
$lang['reports:grand_total_label'] 		= 'Grand Total';

$lang['reports:button_pdf']				= 'PDF';
$lang['reports:button_excel']			= 'Excel';

$lang['accounts:account_lookup_title']	= 'Lookup Account';





