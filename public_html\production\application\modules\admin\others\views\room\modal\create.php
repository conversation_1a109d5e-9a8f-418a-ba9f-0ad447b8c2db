<?php if (!defined('BASEPATH')) exit('No direct script access allowed');
?>
<?php echo form_open(current_url(), array("id" => "form_memo")) ?>

<div class="modal-body">
	<div class="row form-group">
		<div class="row form-group">
			<div class="col-md-12">
				<div class="form-group">
					<label class="control-label col-md-3">No Bed</label>
					<div class="col-md-9">
						<input type="text" name="NoBed" class="form-control" id="NoBed" required="required" value="<?= @$NoBed ?>" autocomplete="off">
					</div>
				</div>
			</div>
		</div>
		<button type="submit" id="submit_memo" class="btn btn-primary"><i class="fa fa-file"></i> Simpan</button>
		<button type="button" class="btn btn-danger" data-dismiss="modal"><i class="fa fa-times"></i> Tutup</button>
	</div>
</div>

<!-- /.modal-dialog -->
<?php echo form_close() ?>

<script type="text/javascript">
	//<![CDATA[
	(function($) {

		$(document).ready(function(e) {

			if ('<?= @$NoBed ?>' != '') {
				selectedData = $("#dt_memo").DataTable().rows(parseInt('<?= $row ?>')).data();
				$("#NoBed").val(selectedData[0].NoBed);
			}

			$("form[id=\"form_memo\"]").on("submit", function(e) {
				e.preventDefault();

				$("#submit_memo").prop("disabled", true);

				var d = new Date();
				var data_post = {};
				data_post['f'] = {
					"NoBed": $("#NoBed").val(),
					"Status": "VC",
				};

				if ('<?= $NoBed ?>' == '') {
					$.alert_success("<?= lang('room:bed_added') ?>");
					$("#dt_memo").DataTable().row.add(data_post['f']).draw(true);
				} else {
					$.alert_success("<?= lang('room:bed_updated') ?>");
					$("#dt_memo").DataTable().row(parseInt('<?= $row ?>')).data(data_post['f']).draw();
				}

				// Close Form
				$('#form-ajax-modal').remove();
				$("body").removeClass("modal-open").removeAttr("style");

			});

		});

	})(jQuery);
	//]]>
</script>