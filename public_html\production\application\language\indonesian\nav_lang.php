<?php
defined('BASEPATH') or exit('No direct script access allowed');

$lang['nav_title:panel'] = 'Panel';
$lang['nav_title:system'] = 'System';
$lang['nav_title:restful'] = 'RESTful WS';

$lang['nav'] = 'Navigasi';
$lang['nav:heading_panel'] = 'Panel';
$lang['nav:dashboard'] = 'Dasbor';
$lang['nav:servings'] = 'Layanan';
$lang['nav:transaction'] = 'Transaksi';
$lang['nav:transactions'] = 'Transaksi';
$lang['nav:references'] = 'Referensi';
$lang['nav:preferences'] = 'Preferensi';
$lang['nav:equipments'] = 'Perangkat';
$lang['nav:reports'] = 'Laporan';
$lang['nav:setup'] = 'Setup';
$lang['nav:settings'] = 'Setingan';
$lang['nav:common'] = 'Data Utama';
$lang['nav:master'] = 'Data Master';
$lang['nav:system'] = 'Sistem';
$lang['nav:users'] = 'Pengguna';
$lang['nav:help'] = 'Bantuan';
$lang['nav:support'] = 'Dukungan';


$lang['nav:panel'] = 'Panel';
$lang['nav:dashboard'] = 'Dashboard';
$lang['nav:restful'] = 'RESTful';
$lang['nav:configs'] = 'Pengaturan';
$lang['nav:users'] = 'Pengguna';
$lang['nav:my_profile'] = 'Profil Saya';
$lang['nav:help'] = 'Bantuan';
$lang['nav:ticket'] = 'Tiket';
$lang['nav:submit_ticket'] = 'Kirim Tiket';
$lang['nav:signin'] = 'Masuk';
$lang['nav:login'] = 'Masuk';
$lang['nav:signout'] = 'Keluar';
$lang['nav:logout'] = 'Keluar';

$lang["restful:access"] = "Access";
$lang["restful:keys"] = "Keys";
$lang["restful:limits"] = "Limits";
$lang["restful:controllers"] = "Controllers";
$lang["restful:uris"] = "URIs";
$lang["restful:logs"] = "Logs Report";

$lang["nav:reports"] = "Laporan";

//Registration
$lang['nav:reservation'] = 'Reservasi';
$lang['nav:registration'] = 'Registrasi';
$lang['nav:transfer_inpatient'] = 'Transfer Rawat Inap';
$lang['nav:cashier'] = 'Kasir';
$lang['nav:drug_realization'] = 'Realisasi Obat';
$lang['nav:drug_payment'] = 'Pembayaran Obat';
$lang['nav:return_drug_realization'] = 'Retur Realisasi Obat';
$lang['nav:outstanding_payment'] = 'Pembayaran Hutang';
$lang['nav:petty_cash'] = 'Petty Cash';
$lang['nav:non_invoice_receipt'] = 'Penerimaan Non Invoice';
$lang['nav:non_invoice_cash_expense'] = 'Pengeluaran Kas Non Invoice';
$lang['nav:bank_cash_deposit'] = 'Setoran Kas Ke Bank';
$lang['nav:schedule'] = 'Jadwal Praktek';
$lang['nav:patients'] = 'Pasien';
$lang['nav:report_patient_reservations'] = 'Pasien Reservasi';
$lang['nav:report_polyclinic_registrations'] = 'Pendaftaran Poliklinik';
$lang['nav:report_registration_amount_per_patient_type'] = 'Registrasi Tipe Pasien';
$lang['nav:report_transaction_recap'] = 'Rekap Transaksi';
$lang['nav:report_transaction_recap_by_section_doctor'] = 'Rekap Transaksi Section &amp; Dokter';
$lang['nav:report_transaction_recap_by_service_group'] = 'Rekap Transaksi Section &amp; Dokter dengan Grup Jasa';

// Pharmacy
$lang['nav:pharmacy'] = 'Farmasi';
$lang['nav:selling'] = 'Penjualan Obat';
$lang['nav:stock_report'] = 'Laporan Stok';
$lang['nav:report_warehouse_cards'] = 'Kartu Gudang';
$lang['nav:report_recap_stocks'] = 'Rekap Stok';
$lang['nav:report_stock_opname'] = 'Stock Opname';
$lang['nav:report_recap_transactions'] = 'Rekap Transaksi';
$lang['nav:report_total_sales'] = 'Total Penjualan';
$lang['nav:report_drug_sales'] = 'Penjualan Obat Bebas';
$lang['nav:report_drug_sale_patient_types'] = 'Penjualan Obat Per Tipe Pasien';
$lang['nav:report_drug_sale_suppliers'] = 'Penjualan Obat Per Supplier';

$lang['nav:performance_report'] = 'Laporan Kinerja';
$lang['nav:unit_performance_report'] = 'Laporan Kinerja Unit';
$lang['nav:turnover_report'] = 'Laporan Omset';
$lang['nav:patient_symptom_therapi'] = 'Laporan Symptom Therapi Pasien';

// Inquiry
$lang['nav:inquiry'] = 'Amprah';
$lang['nav:mutation'] = 'Mutasi';
$lang['nav:mutation_return'] = 'Retur Mutasi';
$lang['nav:stock_opname'] = 'Stock Opname';

// Poli
$lang['nav:outpatient'] = 'Rawat Jalan';
$lang['nav:inpatient'] = 'Rawat Inap';
$lang['nav:laboratory'] = 'Lab';
$lang['nav:examination'] = 'Periksa';
$lang['nav:item_usage'] = 'Pemakaian Barang';

// INVENTORY
$lang['nav:inventory'] = 'Inventaris';
$lang['nav:products'] = 'Produk';
$lang['nav:search_products'] = 'Cari Produk';
$lang['nav:submit_products'] = 'Submit Produk';
$lang['nav:product_unit'] = 'Satuan Obat';
$lang['nav:search_unit'] = 'Cari Satuan';
$lang['nav:submit_unit'] = 'Submit Satuan';
$lang['nav:product_group'] = 'Grup Obat';
$lang['nav:search_group'] = 'Cari Grup';
$lang['nav:submit_group'] = 'Submit Grup';
$lang['nav:product_group_type'] = 'Tipe Grup Obat';
$lang['nav:search_group_type'] = 'Cari Tipe';
$lang['nav:submit_group_type'] = 'Submit Tipe';
$lang['nav:product_category'] = 'Kategori Obat';
$lang['nav:search_category'] = 'Search Kategori';
$lang['nav:submit_category'] = 'Submit Kategori';
$lang['nav:product_class'] = 'Kelas Obat';
$lang['nav:search_class'] = 'Cari Kelas';
$lang['nav:submit_class'] = 'Submit Kelas';
$lang['nav:drugs'] = 'Obat';

$lang['nav:stock'] = 'Stok';
$lang['nav:stock_in'] = 'Stok Masuk';
$lang['nav:stock_out'] = 'Stok Keluar';

// Family Folder
$lang['nav:folder'] = 'Folder';
$lang['nav:family_folder'] = 'Family Folder';
$lang['nav:family_manage'] = 'Manage Family';
$lang['nav:personal'] = 'Personal';

// Nav
$lang['nav:heading_inventory'] = 'Persediaan';
$lang['nav:inv'] = 'Persediaan';
$lang['nav:inventory'] = 'Inventori';
$lang['nav:transactions'] = 'Transaksi';
$lang['nav:references'] = 'Referensi';
$lang['nav:preferences'] = 'Preferensi';
$lang['nav:behaviors'] = 'Alat';
$lang['nav:master'] = 'Master Data';
$lang['nav:setup'] = 'Setup';
$lang['nav:tools'] = 'Alat';
$lang['nav:reports'] = 'Laporan';
$lang['nav:help'] = 'Help';
$lang['nav:subcategory'] = 'Sub Kategori';

// Verifikasi
$lang['nav:heading_verification'] = 'Verifikasi';
$lang['nav:verification'] = 'Verifikasi';
$lang['nav:revenue_recognition'] = 'Pengakuan Pendapatan';


// Back Office
$lang['nav:back_office'] = 'back Office';
$lang['nav:accounting'] = 'Akuntansi';
$lang['nav:accounts'] = 'Master Akun';
$lang['nav:search_accounts'] = 'Cari Akun';
$lang['nav:tree_accounts'] = 'Tampilan Pohon Akun';
$lang['nav:submit_accounts'] = 'Submit Akun';
$lang['nav:account_concept'] = 'Konsep Rekening';
$lang['nav:account_structure'] = 'Struktur Rekening';
$lang['nav:account_income_loss'] = 'Setup Rekening Laba Rugi';


$lang['nav:general_ledger'] = 'General Ledger';
$lang['nav:ledger'] = 'Buku Besar';
$lang['nav:journal_transaction'] = 'Transaksi Jurnal';
$lang['nav:view_journal'] = 'Lihat Jurnal';
$lang['nav:search_journal_transactions'] = 'Cari Transaksi Jurnal';
$lang['nav:submit_journal_transaction'] = 'Submit Transaksi Jurnal';
$lang['nav:beginning_balances'] = 'Saldo Awal Sistem';
$lang['nav:close_books'] = 'Tutup Buku';
$lang['nav:cancel_close_books'] = 'Batal Tutup Buku';
$lang['nav:balance_sheets'] = 'Neraca';
$lang['nav:trial_balance'] = 'Neraca Saldo';
$lang['nav:income_loss'] = 'Laba Rugi';
$lang['nav:income_loss_quarterly'] = 'Laba Rugi Triwulan';
$lang['nav:income_loss_annual'] = 'Laba Rugi Tahunan';
$lang['nav:income_loss_setup'] = 'Setup Laba Rugi';
$lang['nav:cash_flow'] = 'Cash Flow';
$lang['nav:cash_flow_setup'] = 'Setup Cash Flow';
$lang['nav:cash_flow_account'] = 'Rekening Cash Flow';
$lang['nav:cash_flow_report'] = 'Laporan Cash Flow';

$lang['nav:payable'] = 'Hutang';
$lang['nav:payable_types'] = 'Tipe Hutang';
$lang['nav:search_payable_types'] = 'Cari Tipe Hutang';
$lang['nav:submit_payable_type'] = 'Submit Tipe Hutang';
$lang['nav:payable_factur'] = 'Faktur';
$lang['nav:payable_voucher'] = 'Voucher';
$lang['nav:payable_credit_debit_notes'] = 'Nota Debit Kredit';
$lang['nav:payable_posting'] = 'Posting';
$lang['nav:payable_posting_cancel'] = 'Batal Posting';
$lang['nav:payable_close_book'] = 'Tutup Buku';
$lang['nav:payable_report'] = 'Laporan';
$lang['nav:payable_aging'] = 'Penuaan Hutang';
$lang['nav:payable_report_card'] = 'Kartu Hutang';
$lang['nav:payable_report_recap'] = 'Rekap Hutang';

$lang['nav:receivable'] = 'Piutang';
$lang['nav:receivable_types'] = 'Tipe Piutang';
$lang['nav:search_receivable_types'] = 'Cari Tipe Piutang';
$lang['nav:submit_receivable_type'] = 'Submit Tipe Piutang';
$lang['nav:receivable_factur'] = 'Faktur';
$lang['nav:receivable_invoice'] = 'Invoice';
$lang['nav:receivable_credit_debit_notes'] = 'Nota Debit Kredit';
$lang['nav:receivable_posting'] = 'Posting';
$lang['nav:receivable_posting_cancel'] = 'Batal Posting';
$lang['nav:receivable_close_book'] = 'Tutup Buku';
$lang['nav:receivable_report'] = 'Laporan';
$lang['nav:receivable_aging'] = 'Penuaan Piutang';
$lang['nav:receivable_report_card'] = 'Kartu Piutang';
$lang['nav:receivable_report_recap'] = 'Rekap Piutang';

$lang['nav:general_cashier'] = 'General Cashier';
$lang['nav:cash_bank_income'] = 'Penerimaan Kas Bank';
$lang['nav:cash_bank_income_invoice'] = 'Penerimaan Kas Bank Invoice';
$lang['nav:cash_bank_income_non_invoice'] = 'Penerimaan Kas Bank Non Invoice';
$lang['nav:cash_bank_expense'] = 'Pengeluaran Kas Bank';
$lang['nav:cash_bank_expense_voucher'] = 'Pengeluaran Kas Bank Voucher';
$lang['nav:cash_bank_expense_non_voucher'] = 'Pengeluaran Kas Bank Non Voucher';
$lang['nav:cash_bank_mutation'] = 'Mutasi Cash Bank';
$lang['nav:general_cashier_posting'] = 'Posting';
$lang['nav:general_cashier_posting_cancel'] = 'Batal Posting';
$lang['nav:general_cashier_report'] = 'Laporan';
$lang['nav:general_cashier_report_cash'] = 'Kas';
$lang['nav:general_cashier_report_bank'] = 'Bank';
$lang['nav:general_cashier_report_account'] = 'Akun';

//Admin
$lang['nav:admin'] = 'Admin';
$lang['nav:integration_insurance'] = 'Integrasi Asuransi';
$lang['nav:insurance'] = 'Asuransi';
$lang['nav:bpjs_pcare'] = 'BPJS Pcare';

//Assesmen
$lang['nav:beginning_assesment'] = 'Asesmen Awal';
