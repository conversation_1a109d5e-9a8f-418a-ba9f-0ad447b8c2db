<?php defined('BASEPATH') or exit('No direct script access allowed');

date_default_timezone_set("Asia/Hong_Kong");
?>
<!DOCTYPE html>
<html lang="<?php echo lang('lang_code') ?>" class="app">

<head>
	<meta charset="utf-8" />
	<title><PERSON><PERSON> Antrian</title>
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	<meta name="author" content="<?php echo $this->config->item('site_author'); ?>">
	<meta name="keyword" content="<?php echo $this->config->item('site_desc'); ?>">
	<meta name="description" content="">
	<meta name="mobile-web-app-capable" content="yes">

	<link rel="apple-touch-icon" sizes="57x57" href="<?php echo base_url("themes/default/assets/img/favicon"); ?>/apple-icon-57x57.png">
	<link rel="apple-touch-icon" sizes="60x60" href="<?php echo base_url("themes/default/assets/img/favicon"); ?>/apple-icon-60x60.png">
	<link rel="apple-touch-icon" sizes="72x72" href="<?php echo base_url("themes/default/assets/img/favicon"); ?>/apple-icon-72x72.png">
	<link rel="apple-touch-icon" sizes="76x76" href="<?php echo base_url("themes/default/assets/img/favicon"); ?>/apple-icon-76x76.png">
	<link rel="apple-touch-icon" sizes="114x114" href="<?php echo base_url("themes/default/assets/img/favicon"); ?>/apple-icon-114x114.png">
	<link rel="apple-touch-icon" sizes="120x120" href="<?php echo base_url("themes/default/assets/img/favicon"); ?>/apple-icon-120x120.png">
	<link rel="apple-touch-icon" sizes="144x144" href="<?php echo base_url("themes/default/assets/img/favicon"); ?>/apple-icon-144x144.png">
	<link rel="apple-touch-icon" sizes="152x152" href="<?php echo base_url("themes/default/assets/img/favicon"); ?>/apple-icon-152x152.png">
	<link rel="apple-touch-icon" sizes="180x180" href="<?php echo base_url("themes/default/assets/img/favicon"); ?>/apple-icon-180x180.png">
	<link rel="shortcut icon" type="image/png" sizes="192x192" href="<?php echo base_url("themes/default/assets/img/favicon"); ?>/android-icon-192x192.png">
	<link rel="icon" type="image/png" sizes="32x32" href="<?php echo base_url("themes/default/assets/img/favicon"); ?>/favicon-32x32.png">
	<link rel="icon" type="image/png" sizes="96x96" href="<?php echo base_url("themes/default/assets/img/favicon"); ?>/favicon-96x96.png">
	<link rel="icon" type="image/png" sizes="16x16" href="<?php echo base_url("themes/default/assets/img/favicon"); ?>/favicon-16x16.png">
	<link rel="manifest" href="<?php echo base_url("themes/default/assets/img/favicon"); ?>/manifest.json">
	<meta name="msapplication-TileColor" content="#ffffff">
	<meta name="msapplication-TileImage" content="<?php echo base_url("themes/default/assets/img/favicon"); ?>/ms-icon-144x144.png">
	<meta name="theme-color" content="#ffffff">

	<link rel="stylesheet" type="text/css" href="<?php echo base_url(); ?>themes/intuitive/assets/css/green-white.css">
	<link rel="stylesheet" type="text/css" href="<?php echo base_url(); ?>themes/intuitive/assets/css/green-white-custom.css">
	<link rel="stylesheet" type="text/css" href="<?php echo base_url(); ?>themes/intuitive/assets/css/dev-plugins/font-awesome/font-awesome.min.css">

	<!-- javascripts -->
	<script type="text/javascript" src="<?php echo base_url(); ?>themes/intuitive/assets/js/plugins/jquery/jquery.min.js"></script>
	<script type="text/javascript" src="<?php echo base_url(); ?>themes/intuitive/assets/js/plugins/modernizr/modernizr.js"></script>
	<script type="text/javascript" src="<?php echo base_url(); ?>themes/intuitive/assets/js/plugins/bootstrap/bootstrap.min.js"></script>
	<script type="text/javascript" src="<?php echo base_url(); ?>themes/intuitive/assets/js/plugins/moment/moment.js"></script>
	<!-- ./javascripts -->

</head>

<style>
	.max-height {
		height: 100% !important;
	}

	.max-width {
		width: 100% !important;
	}

	.top-left-padding {
		padding-left: 1%;
		padding-top: 1%;
		padding-bottom: 1%;
	}

	.top-right-padding {
		padding-right: 1%;
		padding-top: 1%;
		padding-bottom: 1%;
	}

	.bot-menu-padding {
		padding-top: unset !important;
		padding-bottom: 1% !important;
		padding-left: 1%;
		padding-right: 1%;
	}

	.bot-left-padding {
		padding-left: 1%;
	}

	.bot-right-padding {
		padding-right: 1%;
	}

	.header {
		z-index: 2;
		height: 10%;
		background-color: black;
	}

	/* .body {
		height: 85%;
		background-image: linear-gradient(to bottom, #74ebd5, #96ebc3, #b3eab5, #cee8ad, #e5e5ac);
	} */
	.body {
		height: 85%;
		background-image: linear-gradient(to bottom, #2ea6d5, #2a88ad, #2ea6d5, #2f9ec9, #54bde7);
	}

	.body-top {
		height: 50%;
	}

	.body-middle {
		height: 24%;
	}

	.body-bot {
		height: 26%;
	}

	.bottom {
		z-index: 2;
		background-color: black;
		height: 5%;
	}

	.text-white {
		color: white;
	}

	.box-queue_active {
		padding: 5%;
	}

	.box-section {
		padding: 3%;
	}

	.box-top {
		height: 30%;
	}

	.box-bot {
		height: 50%;
	}

	.body-box-top {
		height: 35%;
		color: yellow;
	}

	.box-top-patient {
		color: yellow;
	}

	.body-box-bot {
		height: 65%;
		color: yellow;
	}

	.tile {
		z-index: 2;
		border-radius: 4px !important;
	}

	.bg-blue-strong {
		background-color: #063c50 !important;
		border-color: #125873 !important;
	}

	.tile.tile-info-middle {
		border: 1px solid #0c4f68 !important;
	}

	.bold {
		font-weight: bold;
	}

	.bg-blue {
		background-color: #125873 !important;
		border-color: #125873 !important;
	}

	.bg-bubbles {
		position: absolute;
		top: 0;
		left: 0;
		width: 95%;
		height: 75%;
		z-index: 1;
	}

	.bg-bubbles li {
		position: absolute;
		list-style: none;
		display: block;
		width: 40px;
		height: 40px;
		background-color: rgba(255, 255, 255, 0.15);
		bottom: -160px;
		-webkit-animation: square 25s infinite;
		animation: square 25s infinite;
		-webkit-transition-timing-function: linear;
		transition-timing-function: linear;
	}

	.bg-bubbles li:nth-child(1) {
		left: 10%;
	}

	.bg-bubbles li:nth-child(2) {
		left: 20%;
		width: 80px;
		height: 80px;
		-webkit-animation-delay: 2s;
		animation-delay: 2s;
		-webkit-animation-duration: 17s;
		animation-duration: 17s;
	}

	.bg-bubbles li:nth-child(3) {
		left: 25%;
		-webkit-animation-delay: 4s;
		animation-delay: 4s;
	}

	.bg-bubbles li:nth-child(4) {
		left: 40%;
		width: 60px;
		height: 60px;
		-webkit-animation-duration: 22s;
		animation-duration: 22s;
		background-color: rgba(255, 255, 255, 0.25);
	}

	.bg-bubbles li:nth-child(5) {
		left: 70%;
	}

	.bg-bubbles li:nth-child(6) {
		left: 80%;
		width: 120px;
		height: 120px;
		-webkit-animation-delay: 3s;
		animation-delay: 3s;
		background-color: rgba(255, 255, 255, 0.2);
	}

	.bg-bubbles li:nth-child(7) {
		left: 32%;
		width: 160px;
		height: 160px;
		-webkit-animation-delay: 7s;
		animation-delay: 7s;
	}

	.bg-bubbles li:nth-child(8) {
		left: 55%;
		width: 20px;
		height: 20px;
		-webkit-animation-delay: 15s;
		animation-delay: 15s;
		-webkit-animation-duration: 40s;
		animation-duration: 40s;
	}

	.bg-bubbles li:nth-child(9) {
		left: 25%;
		width: 10px;
		height: 10px;
		-webkit-animation-delay: 2s;
		animation-delay: 2s;
		-webkit-animation-duration: 40s;
		animation-duration: 40s;
		background-color: rgba(255, 255, 255, 0.3);
	}

	.bg-bubbles li:nth-child(10) {
		left: 90%;
		width: 160px;
		height: 160px;
		-webkit-animation-delay: 11s;
		animation-delay: 11s;
	}

	@-webkit-keyframes square {
		0% {
			-webkit-transform: translateY(0);
			transform: translateY(0);
		}

		100% {
			-webkit-transform: translateY(-700px) rotate(600deg);
			transform: translateY(-700px) rotate(600deg);
		}
	}

	@keyframes square {
		0% {
			-webkit-transform: translateY(0);
			transform: translateY(0);
		}

		100% {
			-webkit-transform: translateY(-700px) rotate(600deg);
			transform: translateY(-700px) rotate(600deg);
		}
	}
</style>

<body class="max-width max-height modal-open">
	<div class="dev-page-header header bg-blue-strong">
		<div class="max-height">
			<img src="<?= base_url('/themes/default/assets/img/logo.png') ?>" class="max-height" id="logo">
			<span id="today" class="max-height text-white text-center" style="float:right"><span id="running-time"></span><br><b><?= date('d F Y') ?></b></span>
		</div>
	</div>
	<div class="audioAntrean">

	</div>
	<button type="button" id="playAnteran" style="display:none" onclick="document.getElementById('player').play()"></button>


	<div class="dev-page-container body ">

		<!-- page content container -->
		<div class="body-top">
			<div id="calling-box" class="col-sm-4 col-xs-12 max-height top-left-padding">
				<a class="tile tile-info box-queue_active bg-blue" style="height: 100%;">
					<div class="body-box-top bold"><span style="margin-bottom: 20px;color:#fff!important;font-size: 45px;">NRM </span></div>
					<div class="body-box-top bold"><span id="queue_nrm"></span></div>

				</a>
			</div>
			<div class="col-sm-8 col-xs-12 max-height max-height top-right-padding">
				<a class="tile tile-default max-height bg-blue" style="padding:unset;">
					<div class="body-box-bot bold" style="padding-top: 50px;">
						<span id="queue_poli" style="font-size: 50px; display: block;">POLI</span>
						<span id="queue_ruangan" style="font-size: 50px; display: block;">RUANGAN</span>
					</div>
				</a>
			</div>
		</div>
		<div class="body-middle">
			<div id="queue-container" class="col-sm-12 col-xs-12 max-height bot-menu-padding">
				<a class="tile tile-info max-height box-section bg-blue">
					<div class="box-top-patient bold"> <span id="queue_patient_name" style="text-transform: uppercase;">NAMA PASIEN</span></div>
				</a>
			</div>
		</div>
		<div class="body-bot">
			<?php foreach ($option_section as $key => $val) { ?>
				<div id="<?= $val->SectionID ?>" class="col-sm-<?= $column_width ?> col-xs-12 max-height <?= $key == 0 ? "bot-left-padding" : "" ?> <?= $key == count($option_section) - 1 ? "bot-right-padding" : "" ?> bot-menu-padding">
					<a class="tile tile-info-middle max-height box-section bg-blue">
						<div class="box-top bold"><span><?= $val->SectionName ?></span></div>
						<div class="box-bot"><span id="queue-number<?= $val->SectionID ?>"><?= @$val->queue ?></span></div>
					</a>
				</div>
			<?php } ?>

		</div>
	</div>

	<div class="bottom bg-blue-strong">
		<marquee class="max-height">
			<h1 class="text-white"><?php echo config_item('klinik_name') ?></h1>
		</marquee>
	</div>
	<ul class="bg-bubbles">
		<li></li>
		<li></li>
		<li></li>
		<li></li>
		<li></li>
		<li></li>
		<li></li>
		<li></li>
		<li></li>
		<li></li>
	</ul>
	<script>
		$(document).ready(function() {
			const height = $("body").height();

			// Responsive styling
			$("marquee h1").css({
				"font-size": height * 0.03,
				margin: 0
			});
			$("marquee").css("padding-top", height * 0.01);
			$("#logo").css("padding", height * 0.01);
			$("#today").css({
				"font-size": height * 0.025,
				"padding": height * 0.01
			});
			$("#running-time").css("font-size", height * 0.035);
			$(".box-top").css("font-size", height * 0.03);
			$(".box-top-patient").css("font-size", height * 0.06);
			$(".box-bot").css("font-size", height * 0.08);
			$(".body-box-top").css("font-size", height * 0.08);
			$(".body-box-bot").css("font-size", height * 0.22);

			// Clock update
			setInterval(() => {
				const now = moment();
				$("#running-time").text(now.format("HH:mm:ss"));
			}, 1000);

			// WebSocket setup with reconnect logic
			const wsUrl = 'wss://<?= config_item("websocket_ip") ?>';
			let socket = null;
			let reconnectInterval = null;

			let queueList = [];
			let isProcessing = false;
			let lastCalledTimes = {}; // Untuk mencegah NoReg dipanggil berulang <60 dETIK

			function connectWebSocket() {
				socket = new WebSocket(wsUrl);

				socket.onopen = () => {
					console.log("✅ WebSocket connected:", wsUrl);
					if (reconnectInterval) {
						clearInterval(reconnectInterval);
						reconnectInterval = null;
					}
				};

				socket.onclose = () => {
					console.warn("🔌 WebSocket disconnected.");
					tryReconnect();
				};

				socket.onerror = (error) => {
					console.error("⚠️ WebSocket error:", error);
					if (socket.readyState !== WebSocket.CLOSED) {
						socket.close(); // Trigger reconnect
					}
				};

				socket.onmessage = (e) => {
					const data = e.data;
					console.log(1);

					if (data === "start_queue" || data === "end_queue") return;
					console.log(2);

					if (data === "queue_refresh") {
						console.log(3);
						$.get("<?= base_url('queue/refresh_queue') ?>", (response) => {
							response.data.forEach((item) => {
								$("#queue-number" + item.SectionID).html(item.queue);
							});
						});
						return;
					}

					console.log(4);
					// Hindari duplikasi antrean
					if (!queueList.includes(data)) {
						console.log(5);
						queueList.push(data);
					}
					console.log("isProcessing", isProcessing);
					console.log("queueList.length", queueList.length);

					if (!isProcessing && queueList.length > 0) {
						console.log(7);
						processRequest(queueList[0]);
					}
				};
			}

			function tryReconnect() {
				if (!reconnectInterval) {
					reconnectInterval = setInterval(() => {
						console.log("🔁 Trying to reconnect WebSocket...");
						connectWebSocket();
					}, 3000);
				}
			}

			function processRequest(queueData) {
				isProcessing = true;

				let parsedData;
				try {
					parsedData = JSON.parse(queueData);
				} catch (e) {
					console.error("❌ Invalid JSON:", e);
					queueList.shift(); // Buang data rusak
					isProcessing = false;
					processNextQueue();
					return;
				}

				// Validasi aksi
				if (!parsedData || parsedData.action !== 'queue_calling') {
					queueList.shift();
					isProcessing = false;
					processNextQueue();
					return;
				}

				// Validasi section
				if (!<?= json_encode($sectionAvailableQueue) ?>.includes(parsedData.SectionID)) {
					queueList.shift();
					isProcessing = false;
					processNextQueue();
					return;
				}

				const now = Date.now();
				const lastCalled = lastCalledTimes[parsedData.NoReg];

				if (lastCalled && (now - lastCalled) < 15 * 1000) {
					const remainingTime = Math.round(15 - (now - lastCalled) / 1000);
					console.warn(`⏳ NoReg ${parsedData.NoReg} dipanggil kurang dari 15 detik lalu. Sisa: ${remainingTime}s`);
					if (socket && socket.readyState === WebSocket.OPEN) {
						socket.send(JSON.stringify(["called_delay", parsedData.NoReg, remainingTime]));
					}
					queueList.shift();
					isProcessing = false;
					setTimeout(processNextQueue, 1000);
					return;
				}

				const payload = {
					patient: {
						NoReg: parsedData.NoReg,
						SectionID: parsedData.SectionID,
						Ruangan: parsedData.Ruangan
					}
				};

				$.post("<?= base_url('queue/queue_calling_new') ?>", payload, (response) => {
					if (response.status === "success") {
						lastCalledTimes[parsedData.NoReg] = Date.now();
						processCalling(response, parsedData.NoReg);
					} else {
						console.error("❌ Gagal memproses antrean:", response.message);
						queueList.shift();
						isProcessing = false;
						setTimeout(processNextQueue, 1000);
					}
				}).fail(() => {
					console.error("❌ AJAX error saat memanggil antrean.");
					queueList.shift();
					isProcessing = false;
					setTimeout(processNextQueue, 1000);
				});
			}

			function processCalling(response, NoReg) {
				const {
					NamaPasien,
					SectionName,
					Ruangan,
					NRM
				} = response.data;
				const audioHTML = response.html;

				if (socket && socket.readyState === WebSocket.OPEN) {
					socket.send(JSON.stringify(["called_queue", NoReg]));
				}

				$(".audioAntrean").html("").append(audioHTML);
				$("#playAnteran").click();

				$("#queue_patient_name").text(NamaPasien);
				$("#queue_poli").text(SectionName);
				$("#queue_ruangan").text(Ruangan != 99 ? 'Ruangan ' + Ruangan : "");
				$("#queue_nrm").text(NRM);

				$("audio").one("ended", () => {
					queueList.shift();
					isProcessing = false;

					if (socket && socket.readyState === WebSocket.OPEN) {
						socket.send(JSON.stringify(["end_queue", NoReg]));
					}

					setTimeout(processNextQueue, 1000);
				});
			}

			function processNextQueue() {
				if (!isProcessing && queueList.length > 0) {
					processRequest(queueList[0]);
				}
			}

			// Modal close logic
			$(document).on("click", ".close, #start-queue", function(e) {
				e.preventDefault();
				$("#form-ajax-modal").remove();
			});

			// Inisialisasi koneksi WebSocket
			connectWebSocket();
		});
	</script>

</body>

<div class="modal in" id="form-ajax-modal" role="dialog" tabindex="-1" aria-hidden="false" style="display: block; padding-right: 17px;">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header bg-default">
				<button type="button" class="close" data-dismiss="modal">×</button>
				<h1 class="modal-title">Halaman Antrian</h1>
			</div>
			<div class="modal-body">
				<div class="row">
					<h3>Tekan tombol mulai untuk memulai antrian.</h3>
				</div>
			</div>
			<div class="modal-footer">
				<div class="row">
					<div class="form-group">
						<div class="col-md-12">
							<button id="start-queue" type="button" class="btn btn-info btn-block">Mulai</button>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

</html>