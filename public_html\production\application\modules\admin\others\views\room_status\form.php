<?php
if (!defined('BASEPATH'))
	exit('No direct script access allowed');
//print_r($item_lookup);exit;
?>
<style>
	.select2-container .select2-choice {
		display: contents !important;
		line-height: 20px !important;
	}

	.select2-custom {
		display: block !important;
	}

	@media (min-width: 992px) {
		.modal-lg {
			width: 1150px !important;
		}
	}
</style>
<?php echo form_open($form_action, [
	'id' => 'form_room_status',
	'name' => 'form_room_status',
	'rule' => 'form',
	'class' => ''
]); ?>

<div class="row">
	<div class="col-md-offset-2 col-md-8">
		<div class="panel panel-default">
			<div class="panel-heading">
				<div class="panel-bars">
					<ul class="btn-bars">
						<li class="dropdown">
							<a data-toggle="dropdown" class="dropdown-toggle" href="javascript:;">
								<i class="fa fa-bars fa-lg tip" data-placement="left" title="<?php echo lang("actions") ?>"></i>
							</a>
							<ul class="dropdown-menu pull-right" role="menu">
								<li>
									<a href="<?php echo site_url("{$nameroutes}/create"); ?>">
										<i class="fa fa-plus"></i> <?php echo lang('action:add') ?>
									</a>
								</li>
							</ul>
						</li>
					</ul>
				</div>
				<h3 class="panel-title"><?php echo (@$is_edit) ? lang('heading:room_status_update') : lang('heading:room_status_create'); ?></h3>
			</div>
			<div class="panel-body table-responsive">
				<div class="row">
					<div class="col-md-12 col-xs-12">
						<div class="form-group">
							<?php echo form_label('No Kamar' . ' *', 'NoKamar', ['class' => 'control-label col-md-3']) ?>
							<div class="col-md-9">
								<?php @$dropdown_room[''] = lang('global:select-none') ?>
								<?php echo form_dropdown('f[NoKamar]', @$dropdown_room, set_value('f[NoKamar]', @$item->NoKamar, TRUE), [
									'id' => 'NoKamar',
									'class' => 'form-control select2'
								]); ?>
							</div>
						</div>
						<div class="form-group">
							<?php echo form_label('BED' . ' *', 'Bed', ['class' => 'control-label col-md-3']) ?>
							<div class="col-md-9">
								<?php @$dropdown_bed[''] = lang('global:select-none') ?>
								<?php echo form_dropdown('f[Bed]', @$dropdown_bed, set_value('f[Bed]', @$item->Bed, TRUE), [
									'id' => 'Bed',
									'class' => 'form-control select2'
								]); ?>
							</div>
						</div>
						<div class="form-group">
							<?php echo form_label('Tanggal' . ' *', 'Tanggal', ['class' => 'control-label col-md-3']) ?>
							<div class="col-md-9">
								<?php echo form_input('f[Tanggal]', set_value('f[Tanggal]', @$item->Tanggal, TRUE), [
									'id' => 'Tanggal',
									'placeholder' => '',
									'class' => 'form-control datepicker',
								]); ?>
							</div>
						</div>
						<div class="form-group">
							<?php echo form_label('Status' . ' *', 'Status', ['class' => 'control-label col-md-3']) ?>
							<div class="col-md-9">
								<?php echo form_dropdown('f[Status]', @$dropdown_status, set_value('f[Status]', @$item->Status, TRUE), [
									'id' => 'Status',
									'class' => 'form-control select2'
								]); ?>
							</div>
						</div>
						<div class="form-group">
							<?php echo form_label('Keterangan' . ' *', 'Keterangan', ['class' => 'control-label col-md-3']) ?>
							<div class="col-md-9">
								<?php echo form_textarea('f[Keterangan]', @$item->Keterangan, [
									'placeholder' => '',
									'id' => 'Keterangan',
									'class' => 'form-control',
								]); ?>
							</div>
						</div>
					</div>
				</div>
				<hr />
				<div class="row">
					<div class="col-md-12">
						<div class="form-group text-right">
							<button id="js-btn-submit" type="submit" class="btn btn-primary"><?php echo lang('buttons:save') ?></button>
							<button class="btn btn-warning" type="button" onclick="window.location='<?php echo base_url("{$nameroutes}/create") ?>';">New</button>
							<button class="btn btn-default" type="button" onclick="window.location='<?php echo base_url("{$nameroutes}") ?>';">Close</button>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
<?php echo form_close() ?>

<script src="<?php echo site_url("themes/bracketadmin/vendor/lookupbox7/jquery.lookupbox7.js"); ?>"></script>
<script type="text/javascript">
	//<![CDATA[
	(function($) {
		var _form = $("#form_room_status");

		var _form_actions = {
			init: function() {
				$("select#NoKamar").on('change', function() {

					_target = $('select#Bed');
					$.ajax({
						url: '<?php echo base_url("others/room/dropdown_html_bed") ?>/' + $(this).val(),
						dataType: 'json',
						type: 'GET',
						data: {
							"parent_id": $(this).val()
						},
						beforeSend: function(xhr, settings) {

							_target.html("");
							$("<option></option>")
								.val("0")
								.text("Loading...")
								.attr('selected', 'selected')
								.appendTo(_target);
							_target.trigger('change');
						},
						success: function(response, status, xhr) {
							_target.html(response).trigger('change');;
						},
						error: function(xhr, msg) {}
					});
				});

			}
		}

		$(document).ready(function(e) {
			_form_actions.init();
			$("select#NoKamar").trigger('change')

			_form.on("submit", function(e) {
				e.preventDefault();

				$.post(_form.prop("action"), _form.serializeArray(), function(response, status, xhr) {

					if ("error" == response.status) {
						$.alert_error(response.message);
						return false
					}

					$.alert_success(response.message);

					var id = response.id;

					setTimeout(function() {

						// document.location.href = "<?php echo base_url($nameroutes); ?>";

					}, 300);

				});
			});
		});

	})(jQuery);
	//]]>
</script>