<?php
if (!defined('BASEPATH'))
	exit('No direct script access allowed');
//print_r($item_lookup);exit;
?>
<?php echo form_open($form_action, [
	'id' => 'form_room_status',
	'name' => 'form_room_status',
	'rule' => 'form',
	'class' => ''
]); ?>
<div class="modal-body">

	<div class="row">
		<div class="row">
			<div class="col-md-12 col-xs-12">
				<div class="form-group">
					<?php echo form_label('Kode Status Kamar' . ' *', 'Kode_Status_Kamar', ['class' => 'control-label col-md-4']) ?>
					<div class="col-md-8">
						<?php echo form_input([
							'name' => 'f[Kode_Status_Kamar]',
							'value' => set_value('f[Kode_Status_Kamar]', @$item->Kode_Status_Kamar, TRUE),
							'id' => 'Kode_Status_Kamar',
							'placeholder' => '',
							'required' => 'required',
							@$is_edit ? 'readonly' : '' => '',
							'class' => 'form-control',
							'autocomplete' => 'off'
						]); ?>
					</div>
				</div>
				<div class="form-group">
					<?php echo form_label('Nama Status Kamar' . ' *', 'Nama_Status_Kamar', ['class' => 'control-label col-md-4']) ?>
					<div class="col-md-8">
						<?php echo form_input([
							'name' => 'f[Nama_Status_Kamar]',
							'value' => set_value('f[Nama_Status_Kamar]', @$item->Nama_Status_Kamar, TRUE),
							'id' => 'Nama_Status_Kamar',
							'placeholder' => '',
							'required' => 'required',
							'class' => 'form-control',
							'autocomplete' => 'off'
						]); ?>
					</div>
				</div>
				<div class="form-group">
					<?php echo form_label('Nama Status Kamar International' . ' *', 'Nama_Status_Kamar_International', ['class' => 'control-label col-md-4']) ?>
					<div class="col-md-8">
						<?php echo form_input([
							'name' => 'f[Nama_Status_Kamar_International]',
							'value' => set_value('f[Nama_Status_Kamar_International]', @$item->Nama_Status_Kamar_International, TRUE),
							'id' => 'Nama_Status_Kamar_International',
							'placeholder' => '',
							'required' => 'required',
							'class' => 'form-control',
							'autocomplete' => 'off'
						]); ?>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
<div class="modal-footer">
	<div class="text-right">
		<button id="js-btn-submit" type="submit" class="btn btn-primary"><?php echo lang('buttons:save') ?></button>
		<button class="btn btn-default" type="button" data-dismiss="modal">Close</button>
	</div>
</div>
<?php echo form_close() ?>

<script src="<?php echo site_url("themes/bracketadmin/vendor/lookupbox7/jquery.lookupbox7.js"); ?>"></script>
<script type="text/javascript">
	//<![CDATA[
	(function($) {
		var _form = $("#form_room_status");

		$(document).ready(function(e) {
			_form.on("submit", function(e) {
				e.preventDefault();

				$.post(_form.prop("action"), _form.serializeArray(), function(response, status, xhr) {

					if ("error" == response.status) {
						$.alert_error(response.message);
						return false
					}

					$.alert_success(response.message);

					var id = response.id;
					$('[data-dismiss="modal"]').trigger('click');
					setTimeout(function() {

						$('#dt_master_room_status').DataTable().ajax.reload();

					}, 1000);

				});
			});

		});

	})(jQuery);
	//]]>
</script>