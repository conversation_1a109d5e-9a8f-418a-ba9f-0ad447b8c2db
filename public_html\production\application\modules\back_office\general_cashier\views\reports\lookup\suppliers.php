<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');
?>

<div class="modal-dialog">
    <div class="modal-content">
        <div class="modal-header"> 
            <button type="button" class="close" data-dismiss="modal">&times;</button> 
            <h4 class="modal-title"><?php echo lang('suppliers:supplier_lookup_title') ?></h4>
        </div>
        <div class="modal-body">
        	<script type="text/javascript">//<![CDATA[
			function lookupbox_row_selected( response ){
				var _response = JSON.parse(response)
				if( _response ){
					
					try{
					
						$("#supplier_id").val( _response.id );
						$("#supplier_code").val( _response.supplier_code );
						$("#supplier_name").val( _response.supplier_name );
						
						var lookup_voucher_url = "<?php echo base_url("payable/credit_debit_notes/lookup_voucher_by_supplier")?>/"+ _response.id;
						$("#lookup_vouchers").attr( "href", lookup_voucher_url );
						$("#lookup_vouchers").removeAttr( "disabled");

					}catch(e){ console.log(e)}

					$( '#lookup-ajax-modal' ).remove();
					
					$("body").removeClass("modal-open");
					
				}
			}
			//]]></script>
            <?php  echo Modules::run( "common/suppliers/lookup", true ) ?>
        </div>
        <div class="modal-footer">
        	<?php echo lang('supplier:supplier_lookup_helper') ?>
        </div>
    </div>
	<!-- /.modal-content -->
</div>
<!-- /.modal-dialog -->

